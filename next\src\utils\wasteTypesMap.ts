/* eslint-disable sonarjs/no-duplicate-string */
export const wasteTypesMap = {
  paper: {
    pictogramName: 'paper',
    className: 'bg-background-waste-paper',
    pictogramClassName: 'text-white',
  },
  plastic: {
    pictogramName: 'plastic',
    className: 'bg-background-waste-plastic',
    pictogramClassName: 'text-content-passive-secondary',
  },
  glass: {
    pictogramName: 'glass',
    className: 'bg-background-waste-glass',
    pictogramClassName: 'text-white',
  },
  civicAmenitySite: {
    pictogramName: 'civicAmenitySite',
    className: 'bg-background-waste-civic-amenity-site',
    pictogramClassName: 'text-content-passive-secondary',
  },
  cookingOilsAndFats: {
    pictogramName: 'cookingOilsAndFats',
    className: 'bg-background-waste-cooking-oils-and-fats',
    pictogramClassName: 'text-white',
  },
  kitchen: {
    pictogramName: 'kitchenWaste',
    className: 'bg-background-waste-kitchen',
    pictogramClassName: 'text-white',
  },
  organic: {
    pictogramName: 'organic',
    className: 'bg-background-waste-organic',
    pictogramClassName: 'text-white',
  },
  mixed: {
    pictogramName: 'mixed',
    className: 'bg-background-waste-mixed',
    pictogramClassName: 'text-white',
  },
  garbageBag: {
    pictogramName: 'garbageBag',
    className: 'bg-background-waste-garbage-bag',
    pictogramClassName: 'text-white',
  },
  cemetery: {
    pictogramName: 'cemetery',
    className: 'bg-background-waste-cemetery',
    pictogramClassName: 'text-white',
  },
  christmasTrees: {
    pictogramName: 'christmasTrees',
    className: 'bg-background-waste-christmas-trees',
    pictogramClassName: 'text-white',
  },
} as const
