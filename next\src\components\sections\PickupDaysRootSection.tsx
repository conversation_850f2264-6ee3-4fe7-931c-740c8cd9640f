import PickupDaysSection from '@/src/components/sections/PickupDays/PickupDaysSection'
import PickupDaysOldDataSection from '@/src/components/sections/PickupDaysOldData/PickupDaysOldDataSection'
import { PickupDaysSectionFragment } from '@/src/services/graphql/api'

type Props = {
  section: PickupDaysSectionFragment
}

const PickupDaysRootSection = ({ section }: Props) => {
  const { displayOldData } = section ?? {}

  return displayOldData ? (
    <PickupDaysOldDataSection section={section} />
  ) : (
    <PickupDaysSection section={section} />
  )
}

export default PickupDaysRootSection
