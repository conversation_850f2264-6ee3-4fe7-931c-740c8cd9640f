import * as NavigationMenu from '@radix-ui/react-navigation-menu'
import React, { forwardRef } from 'react'

import Icon from '@/src/components/lib/Icon/Icon'
import Typography from '@/src/components/lib/Typography/Typography'
import cn from '@/src/utils/cn'

type NavMenuTriggerProps = {
  label: string
  className?: string
}

const NavMenuTrigger = forwardRef<HTMLButtonElement, NavMenuTriggerProps>(
  ({ label, className }, forwardedRef) => {
    return (
      <NavigationMenu.Trigger
        ref={forwardedRef}
        // To disable "onHover" behaviour, needs to be set also in NavMenuContent
        // https://github.com/radix-ui/primitives/issues/1630#issuecomment-1237106380
        onPointerMove={(event) => event.preventDefault()}
        onPointerLeave={(event) => event.preventDefault()}
        className={cn(
          'flex items-center justify-center gap-0.5 px-4 py-5 underline-offset-2 outline-hidden transition ring-inset hover:bg-background-navmenu-trigger-hover focus-visible:ring-3 data-[state=open]:underline',
          className,
        )}
      >
        <Typography variant="p-default-bold">{label}</Typography>
        <Icon name="chevron-dole-maly" className="size-6" aria-hidden />
      </NavigationMenu.Trigger>
    )
  },
)

export default NavMenuTrigger
