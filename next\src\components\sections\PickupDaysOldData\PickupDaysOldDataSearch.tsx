import { keepPreviousData, useQuery } from '@tanstack/react-query'
import { KeyboardEvent, useEffect, useRef, useState } from 'react'
import { useDebounceValue, useOnClickOutside } from 'usehooks-ts'

import AnimateHeight from '@/src/components/common/AnimateHeight/AnimateHeight'
import SearchBar from '@/src/components/lib/SearchBar/SearchBar'
import PickupDaysOldDataListView from '@/src/components/sections/PickupDaysOldData/PickupDaysOldDataListView'
import PickupDaysOldDataSearchResults from '@/src/components/sections/PickupDaysOldData/PickupDaysOldDataSearchResults'
import { filterUniqueResultsBySearchType } from '@/src/components/sections/PickupDaysOldData/utils'
import { PickupDaysSectionFragment } from '@/src/services/graphql/api'
import {
  getMeiliWasteCollectionDaysQueryKey,
  meiliWasteCollectionDaysFetcher,
  wasteCollectionDaysDefaultFilters,
} from '@/src/services/meili/fetchers/wasteCollectionDaysFetcher'
import { useRoutePreservedState } from '@/src/utils/useRoutePreservedState'

const handleOnKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
  if (event.key === 'ArrowDown') {
    document.querySelector<HTMLAnchorElement>('#pickupdays-search-results')?.focus()
    event.preventDefault()
  }
}

type Props = {
  section: PickupDaysSectionFragment
  searchType: 'address' | 'registrationNumber'
}
/**
 * Inspired by search component in Bratislava.sk homepage
 * https://github.com/bratislava/bratislava.sk/blob/master/next/src/components/common/HomepageSearch/HomePageSearch.tsx
 */
const PickupDaysOldDataSearch = ({ section, searchType }: Props) => {
  const [searchValue, setSearchValue] = useState('')
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const [pickedValue, setPickedValue] = useState<string>('')
  const [debouncedInput] = useDebounceValue(searchValue, 300)
  const inputRef = useRef<HTMLDivElement>(null)

  // this casting is needed due to a known bug: https://github.com/juliencrn/usehooks-ts/issues/663#issuecomment-2559964401
  useOnClickOutside<HTMLDivElement>(inputRef as React.RefObject<HTMLDivElement>, () =>
    setIsOpen(false),
  )

  const [filters, setFilters] = useRoutePreservedState(wasteCollectionDaysDefaultFilters)

  const { data, isPending, error } = useQuery({
    queryFn: () => meiliWasteCollectionDaysFetcher(filters),
    queryKey: getMeiliWasteCollectionDaysQueryKey(filters),
    placeholderData: keepPreviousData,
  })

  useEffect(() => {
    setFilters((previousState) => ({ ...previousState, search: debouncedInput, page: 1 }))
  }, [debouncedInput, setFilters])

  useEffect(() => {
    setIsOpen(true)
    setPickedValue('')
  }, [searchValue])

  const handleSearchItemClick = (picked: string) => {
    setIsOpen(false)
    if (picked) setPickedValue(picked)
  }

  return (
    <div ref={inputRef} className="relative">
      <SearchBar
        input={searchValue}
        inputId="pickup-days-search-field"
        setInput={setSearchValue}
        setSearchQuery={(value) =>
          setFilters((previousState) => ({ ...previousState, search: value, page: 1 }))
        }
        isLoading={isPending}
        onKeyDown={handleOnKeyDown}
      />
      {isOpen ? (
        <AnimateHeight isVisible={!!searchValue} className="absolute top-full z-40 mt-2 w-full">
          {searchValue ? (
            <div className="rounded-lg border-1 border-border-passive-primary bg-white py-2">
              <PickupDaysOldDataSearchResults
                focusId="pickupdays-search-results"
                searchResults={filterUniqueResultsBySearchType(data?.hits, searchType)}
                handleSearchItemClick={handleSearchItemClick}
                isPending={isPending}
                error={error}
              />
            </div>
          ) : null}
        </AnimateHeight>
      ) : null}
      {pickedValue ? (
        <div className="py-4">
          <PickupDaysOldDataListView
            pickedValue={pickedValue}
            searchType={searchType}
            section={section}
          />
        </div>
      ) : null}
    </div>
  )
}

export default PickupDaysOldDataSearch
