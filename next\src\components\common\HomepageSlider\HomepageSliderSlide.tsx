import { readableColorIsBlack } from 'color2k'
import Image from 'next/image'
import { useTranslation } from 'next-i18next'
import React from 'react'

import Button from '@/src/components/lib/Button/Button'
import Typography from '@/src/components/lib/Typography/Typography'
import { SlideItemFragment } from '@/src/services/graphql/api'
import cn from '@/src/utils/cn'
import { generateImageSizes } from '@/src/utils/generateImageSizes'
import { useGetLinkProps } from '@/src/utils/useGetLinkProps'

type SlideProps = SlideItemFragment & {
  className?: string
}

const HomepageSliderSlide = ({
  title,
  text,
  link,
  media,
  backgroundColor,
  className,
}: SlideProps) => {
  const { t } = useTranslation()
  const { getLinkProps } = useGetLinkProps()

  const { url, alternativeText } = media?.data?.attributes ?? {}

  const invertedTypographyClassNames = {
    'text-white': !readableColorIsBlack(backgroundColor), // Change the text color to white if contrast with the background is insufficient
  }

  return (
    <div
      style={{ backgroundColor }}
      className={cn('flex size-full flex-col overflow-hidden rounded-xl', className)}
    >
      {url ? (
        <div
          // 20.125rem = 322px
          // Top positioning is used instead of bottom, to prevent the image from falling down on animating
          className="relative z-0 flex aspect-612/322 lg:absolute lg:top-32 lg:right-0 lg:h-[20.125rem]"
        >
          <Image
            src={url}
            alt={alternativeText ?? ''}
            fill
            sizes={generateImageSizes({ default: '100vw', lg: '70vw' })}
            // Use object-contain to always show the whole illustration
            className="object-contain"
          />
        </div>
      ) : null}
      <div className="z-1 h-full px-4 py-6 lg:px-6 lg:py-8">
        <div className="flex flex-col gap-4 lg:gap-6">
          <div className={cn('flex flex-col gap-2 lg:gap-3', invertedTypographyClassNames)}>
            <Typography variant="h3">{title}</Typography>
            {text ? <Typography variant="p-default">{text}</Typography> : null}
          </div>
          {link ? (
            <Button
              // TODO implement and use inverted Button variant
              variant="black-solid"
              asLink
              hasLinkIcon={false}
              {...getLinkProps(link)}
              aria-label={t('common.showMore')}
              className={cn({
                'border-white bg-white text-content-active-secondary-default hover:border-border-active-tertiary-default hover:bg-border-active-tertiary-default hover:text-content-active-secondary-hover':
                  !readableColorIsBlack(backgroundColor),
              })}
            />
          ) : null}
        </div>
      </div>
    </div>
  )
}

export default HomepageSliderSlide
