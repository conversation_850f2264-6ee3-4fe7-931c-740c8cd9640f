import { useTranslation } from 'next-i18next'
import React from 'react'

import <PERSON>lo<PERSON>ogo from '@/src/assets/images/olo-logo.svg'
import Markdown from '@/src/components/formatting/Markdown'
import Button from '@/src/components/lib/Button/Button'
import OloIcon from '@/src/components/lib/Icon/OloIcon'
import Link from '@/src/components/lib/Link/Link'
import { FooterFragment } from '@/src/services/graphql/api'
import { isDefined } from '@/src/utils/isDefined'
import { useGetLinkProps } from '@/src/utils/useGetLinkProps'

export const FooterContacts = ({
  text,
  facebookUrl,
  instagramUrl,
  linkedinUrl,
}: FooterFragment) => {
  const { t } = useTranslation()

  return (
    <div className="flex flex-col flex-wrap gap-6 text-content-passive-inverted-secondary">
      {/* TODO consider extracting logo to a separate component */}
      <OloLogo fill="white" />
      <Markdown content={text} />
      <div className="flex gap-6 text-content-passive-inverted-primary">
        {facebookUrl ? (
          <Button
            variant="icon-wrapped-negative-margin"
            href={facebookUrl}
            asLink
            hasLinkIcon={false}
            icon={<OloIcon name="social-media-facebook-footer" />}
            aria-label={t('footer.aria.facebook')}
          />
        ) : null}

        {instagramUrl ? (
          <Button
            variant="icon-wrapped-negative-margin"
            href={instagramUrl}
            asLink
            hasLinkIcon={false}
            icon={<OloIcon name="social-media-instagram-footer" />}
            aria-label={t('footer.aria.instagram')}
          />
        ) : null}
        {linkedinUrl ? (
          <Button
            variant="icon-wrapped-negative-margin"
            href={linkedinUrl}
            asLink
            hasLinkIcon={false}
            icon={<OloIcon name="social-media-linkedin-footer" />}
            aria-label={t('footer.aria.instagram')}
          />
        ) : null}
      </div>
    </div>
  )
}

export const FooterBottomLinks = ({ bottomLinks }: FooterFragment) => {
  const { getLinkProps } = useGetLinkProps()

  return (
    // eslint-disable-next-line react/jsx-no-useless-fragment
    <>
      {bottomLinks?.filter(isDefined).map((bottomLink, index) => {
        return (
          <Link
            variant="underlined"
            // eslint-disable-next-line react/no-array-index-key
            key={index}
            {...getLinkProps(bottomLink)}
          />
        )
      })}
    </>
  )
}
