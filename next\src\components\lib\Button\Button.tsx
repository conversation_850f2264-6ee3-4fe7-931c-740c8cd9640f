import NextLink from 'next/link'
import { ComponentProps, forwardRef, ReactNode, RefObject } from 'react'
import { AriaButtonProps } from 'react-aria'
import { Button as RACButton, ButtonProps as RACButtonProps } from 'react-aria-components'

import Icon from '@/src/components/lib/Icon/Icon'
import Link, { LinkAnalyticsProps } from '@/src/components/lib/Link/Link'
import Spinner from '@/src/components/lib/Spinner/Spinner'
import cn from '@/src/utils/cn'

/**
 *  Figma: https://www.figma.com/file/2qF09hDT9QNcpdztVMNAY4/OLO-Web?type=design&node-id=4-385&mode=design&t=IDCmW43zimnlwiDU-4
 */

type ButtonOrIconButton =
  | {
      icon: ReactNode
      'aria-label': string
      startIcon?: never
      endIcon?: never
      children?: never
    }
  | {
      icon?: never
      startIcon?: ReactNode
      endIcon?: ReactNode
      children: ReactNode
    }

type ButtonBase = {
  variant:
    | 'unstyled'
    | 'icon-wrapped'
    | 'icon-wrapped-negative-margin'
    | 'category-solid' // Figma: Primary
    | 'category-outline' // Figma: Secondary
    | 'category-plain' // Figma: Plain
    | 'black-solid' // Figma: Primary inverted
    | 'black-outline' // Figma: Tertiary
    | 'black-plain' // not in Figma, but we implement it
    | 'negative-solid' // TODO not in Figma
    | 'negative-plain' // TODO not in Figma
    | 'black-link' // Figma: Default link
  size?: 'responsive' | 'large' | 'small'
  className?: string
  fullWidth?: boolean
  fullWidthMobile?: boolean
  isLoading?: boolean
  loadingText?: string
} & ButtonOrIconButton

export type ButtonProps = Omit<RACButtonProps, 'className' | 'style'> & {
  asLink?: false
  href?: never
  target?: never
  hasLinkIcon?: never
  analyticsProps?: never
}

export type AnchorProps = Omit<AriaButtonProps<'a'>, 'children'> &
  Pick<ComponentProps<typeof NextLink>, 'target' | 'replace' | 'prefetch'> & {
    asLink: true
    stretched?: boolean
    hasLinkIcon?: boolean
    analyticsProps?: LinkAnalyticsProps
  }

export type PolymorphicProps = ButtonBase & (ButtonProps | AnchorProps)

/**
 *  Figma: https://www.figma.com/file/2qF09hDT9QNcpdztVMNAY4/OLO-Web?type=design&node-id=4-385&mode=design&t=IDCmW43zimnlwiDU-4
 */

const Button = forwardRef<HTMLAnchorElement | HTMLButtonElement, PolymorphicProps>(
  (
    {
      children,
      className,
      isDisabled,
      variant = 'unstyled',
      size = 'responsive',
      icon,
      startIcon,
      endIcon,
      hasLinkIcon = true,
      fullWidth,
      fullWidthMobile,
      isLoading,
      loadingText,
      asLink, // not used, but it should not be passed down in `rest` because it's not valid html attribute for <button> or <a>
      ...rest
    },
    ref,
    // eslint-disable-next-line sonarjs/cognitive-complexity
  ) => {
    const isLoadingOrDisabled = isLoading || isDisabled

    const isSolidVariant = variant.endsWith('-solid')
    const isOutlineVariant = variant.endsWith('-outline')
    const isSolidOrOutlineVariant = isSolidVariant || isOutlineVariant
    const isPlainVariant = variant.endsWith('-plain')
    const isLinkVariant = variant.endsWith('-link')
    const isIconWrappedVariant =
      variant === 'icon-wrapped' || variant === 'icon-wrapped-negative-margin'
    const isIconButton = Boolean(icon)

    /* TODO
     *   - text should be styled by Typography component, now we use "text-size-button-default font-bold" directly
     *   - border should render inside button, not outside
     */
    const styles = cn(
      /*
       *  We use isFocusVisible to show focus ring only on keyboard navigation
       *  It's recommended to remove default outline and use custom styling as ring:
       *  https://tailwindcss.com/docs/outline-style#removing-outlines
       */
      'ring-offset-2 outline-hidden transition focus-visible:ring-3',
      variant === 'unstyled'
        ? (className ?? '')
        : cn(
            'inline-flex h-auto items-center justify-center gap-2 text-size-button-default font-bold',

            // we change rounded corners for link focus ring
            isLinkVariant ? 'rounded-xs max-lg:gap-1' : 'rounded-lg',

            {
              // NOTE: there are some style overrides for link variants below in "twMerge"

              'font-normal underline underline-offset-2': isLinkVariant,

              // disabled or loading
              'opacity-50': isLoadingOrDisabled,

              // https://github.com/tailwindlabs/tailwindcss/issues/1041#issuecomment-957425345
              'after:absolute after:inset-0': 'stretched' in rest && rest.stretched,

              // width or fullwidth
              'w-full': fullWidth,
              'w-full md:w-fit': fullWidthMobile,
              'w-fit': !fullWidth && !fullWidthMobile,

              // border width
              border: isSolidOrOutlineVariant,

              // padding - link variants
              'p-0': isLinkVariant,

              // padding - icon-wrapped variant
              'p-2 outline-offset-0': isIconButton && isIconWrappedVariant,
              '-m-2': isIconButton && variant === 'icon-wrapped-negative-margin',

              // padding - filled and outlined variants
              'px-4 py-2 lg:py-3':
                size === 'responsive' && !isIconButton && isSolidOrOutlineVariant,
              'px-4 py-2': size === 'small' && !isIconButton && isSolidOrOutlineVariant,
              'px-4 py-3': size === 'large' && !isIconButton && isSolidOrOutlineVariant,

              // padding - filled and outlined variants with "icon"
              'p-2.5 lg:p-3': size === 'responsive' && isIconButton && isSolidOrOutlineVariant,
              'p-2.5': size === 'small' && isIconButton && isSolidOrOutlineVariant,
              'p-3': size === 'large' && isIconButton && isSolidOrOutlineVariant,

              // padding - plain variants
              'px-2 py-1 lg:px-3 lg:py-2': size === 'responsive' && !isIconButton && isPlainVariant,
              'px-2 py-1': size === 'small' && !isIconButton && isPlainVariant,
              'px-3 py-2': size === 'large' && !isIconButton && isPlainVariant,

              // padding - plain variants with "icon"
              'p-1.5 lg:p-2': size === 'responsive' && isIconButton && isPlainVariant,
              'p-1.5': size === 'small' && isIconButton && isPlainVariant,
              'p-2': size === 'large' && isIconButton && isPlainVariant,

              // colors: idle & focus (background, border, text)

              // TODO content-active-primary and content-active-secondary are not well named now because of consistency with Design System - it needs to be simplified

              // Variant: category-solid (Figma: Primary)
              'border-border-active-primary-default bg-background-active-primary-default text-content-active-primary-inverted-default hover:border-border-active-primary-hover hover:bg-background-active-primary-hover hover:text-content-active-primary-inverted-hover pressed:border-border-active-primary-pressed pressed:bg-background-active-primary-pressed pressed:text-content-active-primary-inverted-pressed':
                variant === 'category-solid',

              // Variant: category-outline (Figma: Secondary)
              'border-border-active-secondary-default bg-background-active-secondary-default text-content-active-secondary-default hover:border-border-active-secondary-hover hover:bg-background-active-secondary-hover hover:text-content-active-secondary-hover pressed:border-border-active-secondary-pressed pressed:bg-background-active-secondary-pressed pressed:text-content-active-secondary-pressed':
                variant === 'category-outline',

              // Variant: category-plain (Figma: Plain)
              'bg-background-active-primary-soft-default text-content-active-primary-inverted-default hover:bg-background-active-primary-soft-hover hover:text-content-active-primary-inverted-hover pressed:bg-background-active-primary-soft-pressed pressed:text-content-active-primary-inverted-pressed':
                variant === 'category-plain',

              // Variant: black-solid (Figma: Primary inverted)
              'border-border-active-primary-inverted-default bg-background-active-primary-inverted-default text-content-active-primary-default hover:border-border-active-primary-inverted-hover hover:bg-background-active-primary-inverted-hover hover:text-content-active-primary-hover pressed:border-border-active-primary-inverted-pressed pressed:bg-background-active-primary-inverted-pressed pressed:text-content-active-primary-pressed':
                variant === 'black-solid',

              // Variant: black-link (Figma: Button link)
              'text-content-active-primary-inverted-default hover:text-content-active-primary-inverted-hover pressed:text-content-active-primary-inverted-pressed':
                variant === 'black-link',

              // Variant: black-outline (Custom, not in Figma, close to Plain inverted)
              // TODO setup proper tokens
              'border-border-active-default hover:border-border-active-primary-inverted-hover pressed:border-border-active-primary-inverted-pressed':
                variant === 'black-outline',

              // Variant: black-plain (Custom, not in Figma)
              // TODO setup proper tokens
              'bg-background-active-primary-soft-inverted-default text-content-active-secondary-default hover:bg-background-active-primary-soft-inverted-hover hover:text-content-active-primary-hover pressed:bg-background-active-primary-soft-inverted-pressed pressed:text-content-active-primary-pressed':
                variant === 'black-plain',

              // svg icons
              '[&>svg]:h-5 [&>svg]:w-5 lg:[&>svg]:h-6 lg:[&>svg]:w-6': size === 'responsive',
              '[&>svg]:h-5 [&>svg]:w-5': size === 'small',
              '[&>svg]:h-6 [&>svg]:w-6': size === 'large',
            },
            className,
          ),
    )

    if (rest.href) {
      const isExternal = rest.href.startsWith('http')
      const linkIcon = hasLinkIcon ? (
        isExternal ? (
          <Icon name="export" />
        ) : (
          <Icon name="sipka-doprava" />
        )
      ) : null

      return (
        <Link
          href={rest.href}
          target={isExternal ? '_blank' : '_self'}
          ref={ref as RefObject<HTMLAnchorElement>}
          className={styles}
          analyticsProps={rest.analyticsProps}
          {...rest}
        >
          {startIcon}
          {icon ?? children}
          {linkIcon ?? endIcon}
        </Link>
      )
    }

    return (
      <RACButton
        ref={ref as RefObject<HTMLButtonElement>}
        isDisabled={isLoadingOrDisabled}
        className={styles}
        {...rest}
      >
        {!isLoading && startIcon}
        {isLoading ? (
          <>
            {loadingText}
            <Spinner size="small" />
          </>
        ) : (
          (icon ?? children)
        )}
        {!isLoading && endIcon}
      </RACButton>
    )
  },
)

export default Button
