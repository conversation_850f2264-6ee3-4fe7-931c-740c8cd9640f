import { Box, ContentLayout, HeaderLayout, Layout, Stack } from '@strapi/design-system'
import ImportSection from '../../components/ImportSection'
import DeleteSection from '../../components/DeleteSection'

const HomePage = () => {
  return (
    <div>
      <Box background="neutral100">
        <Layout>
          <HeaderLayout title="Import odvozových dní (nový)"></HeaderLayout>
          <ContentLayout>
            <Stack spacing={4}>
              <ImportSection type={'pickup-days'} />
              <DeleteSection type={'pickup-days'} />
              <DeleteSection type={'pickup-days'} deleteAll={true}  />
              {/* <UpdateMeilisearchSection type={'pickup-days'} /> TODO: add to meilisearch */}
            </Stack>
          </ContentLayout>
        </Layout>
      </Box>
    </div>
  )
}

export default HomePage
