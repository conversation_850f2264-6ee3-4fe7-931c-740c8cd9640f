import { useState } from 'react'
import axiosInstance from '../utils/axiosInstance'
import { Alert, Box, Button, Link, Loader, Stack, Typography } from '@strapi/design-system'

const deleteUrls = {
  'pickup-days': '/pickup-days-import/delete-pickup-days',
}

const links = {
  'pickup-days': () =>
    `/content-manager/collectionType/api::pickup-day.pickup-day`,
}

type DeleteSectionProps = {
  type: 'pickup-days'
  deleteAll?: boolean
}

const DeleteSection = ({ type, deleteAll = false }: DeleteSectionProps) => {
  const [inputValue, setInputValue] = useState('')

  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState<any>(null)
  const [error, setError] = useState<any>(null)

  const handleSubmit = () => {
    setLoading(true)
    setSuccess(null)
    setError(null)

    axiosInstance
      .post(deleteAll ? `${deleteUrls[type]}/` : `${deleteUrls[type]}/${inputValue}`)
      .then((response) => {
        setSuccess(response)
      })
      .catch((error) => {
        setError(error)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  return (
    <Box
      background="neutral0"
      hasRadius
      shadow="filterShadow"
      paddingTop={6}
      paddingBottom={6}
      paddingLeft={7}
      paddingRight={7}
    >
      <Stack spacing={4}>
        <Typography variant="delta" as="h2">
          {deleteAll ? "Vymazanie všetkých odvozových dní" : "Vymazanie odvozových dní"}
        </Typography>
        {loading && <Loader />}
        {success && (
          <Alert
            title="Vymazanie úspešné"
            action={
              success.data?.importId && <Link to={links[type]()}>Prejsť na odvozové dni</Link>
            }
            variant="success"
            onClose={() => setSuccess(null)}
          >
            {success.data.message}
          </Alert>
        )}
        {error && (
          <Alert title="Vymazanie neúspešné" variant="danger" onClose={() => setError(null)}>
            {error?.response?.data?.message ?? error.toString()}
          </Alert>
        )}

        {!deleteAll && <input
          type="text"
          value={inputValue}
          onChange={(event) => setInputValue(event.target.value)}
        />}

        <div>
          <Button onClick={handleSubmit} loading={loading} disabled={!inputValue && !deleteAll} variant="danger">
            {deleteAll ? "Vymazať všetky odvozové dni" : "Vymazať odvozové dni podľa uvedeného typu"}
          </Button>
        </div>
      </Stack>
    </Box>
  )
}

export default DeleteSection
