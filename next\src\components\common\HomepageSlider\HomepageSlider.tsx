import { AnimatePresence, motion, Variant } from 'framer-motion'
import { useTranslation } from 'next-i18next'
import React from 'react'

import HomepageSliderSlide from '@/src/components/common/HomepageSlider/HomepageSliderSlide'
import Button from '@/src/components/lib/Button/Button'
import Icon from '@/src/components/lib/Icon/Icon'
import { SlideItemFragment } from '@/src/services/graphql/api'
import cn from '@/src/utils/cn'
import { useSlider } from '@/src/utils/useSlider'

export type HomepageSliderProps = {
  slides: SlideItemFragment[]
  autoRotateInterval?: number
  className?: string
}

export const variants: Record<string, Variant> = {
  initial: (direction: number) => {
    return {
      x: direction === 0 ? 100 : -100, // 100 = right, -100 = left
      opacity: 0,
    }
  },
  center: {
    x: 0,
    opacity: 1,
  },
  exit: (direction: number) => {
    return {
      x: direction === 0 ? -100 : 100,
      opacity: 0,
    }
  },
}

export const homepageSliderMotionProps = {
  transition: {
    x: { type: 'spring', stiffness: 300, damping: 30, duration: 0.2 },
    opacity: { duration: 0.2 },
  },
  initial: 'initial',
  animate: 'center',
  exit: 'exit',
}

/**
 * Figma: https://www.figma.com/design/2qF09hDT9QNcpdztVMNAY4/OLO-Web?node-id=2096-19845&t=G8gUCwznUtpSprDI-1
 * Inspired by marianum.sk https://github.com/bratislava/marianum.sk/blob/master/next/components/molecules/Slider.tsx
 * and W3C: https://www.w3.org/WAI/ARIA/apg/patterns/carousel/#tabbed-carousel-elements
 */

const HomepageSlider = ({ slides, autoRotateInterval, className }: HomepageSliderProps) => {
  const { t } = useTranslation()

  const { activeItemIndex, activeItemDirection, handleGoToNext, handleGoToPrevious } = useSlider(
    slides.length,
    autoRotateInterval,
  )

  return (
    <div
      role="tabpanel"
      aria-label={t('carousel.homepage.ariaLabel')}
      className={cn(
        'relative flex h-full flex-col justify-end overflow-hidden rounded-xl lg:col-span-2 lg:row-span-2',
        className,
      )}
      // Using `backgroundColor` also here, not just in slides, to avoid white empty space when sliding
      style={{ backgroundColor: slides[activeItemIndex].backgroundColor }}
    >
      <AnimatePresence initial={false} custom={activeItemDirection} mode="wait">
        <motion.div
          key={activeItemIndex}
          custom={activeItemDirection}
          variants={variants}
          {...homepageSliderMotionProps}
          className="flex flex-col lg:h-full"
        >
          <HomepageSliderSlide {...slides[activeItemIndex]} />
        </motion.div>
      </AnimatePresence>

      {slides.length > 1 ? (
        <ul
          // Inspired by: https://inclusive-components.design/a-content-slider/#thebuttongroup
          role="tablist"
          aria-label={t('carousel.aria.controlButtons')}
          className="absolute bottom-8 z-1 flex gap-2 px-4 lg:gap-3 lg:px-6"
        >
          <li role="tab">
            <Button
              variant="unstyled"
              icon={<Icon name="sipka-dolava" />}
              aria-label={t('carousel.aria.previous')}
              onPress={handleGoToPrevious}
              className="rounded-full bg-white p-2 ring-offset-content-passive-primary"
            />
          </li>
          <li role="tab">
            <Button
              variant="unstyled"
              icon={<Icon name="sipka-doprava" />}
              aria-label={t('carousel.aria.next')}
              onPress={handleGoToNext}
              className="rounded-full bg-white p-2 ring-offset-content-passive-primary"
            />
          </li>
        </ul>
      ) : null}
    </div>
  )
}

export default HomepageSlider
