@import 'tailwindcss';

@plugin 'tailwind-scrollbar-hide';
@plugin 'tailwindcss-react-aria-components';
@plugin 'tailwindcss-animate';
@plugin '@tailwindcss/typography';

/* Docs: https://tailwindcss.com/docs/functions-and-directives */

@theme {
  /* BREAKPOINTS */

  --breakpoint-sm: 40rem; /* 640px  - phone landscape */
  --breakpoint-md: 48rem; /* 768px  - iPad portrait   */
  --breakpoint-lg: 64rem; /* 1024px - iPad landscape  */
  --breakpoint-xl: 80rem; /* 1280px - small desktop   */

  /* SPACING */

  --spacing-mobileNavBar: 3.7rem; /* 59.52px - excluding the border */

  /* COLORS */

  /*
    These variables are used in different pages and component to override
    colors of different things. They are defined as RGB values on purpose
    so they can be used in `rgb(var(--color-main-600))` style or with transparency
    as `rgba(var(--color-main-600), 0.25)`.
  */

  --color-transparent: transparent;
  --color-current: currentColor;
  --color-white: var(--color-grey-0);
  --color-font: var(--color-content-passive-primary);

  /* OLO brand yellow */
  --color-brand-800: rgb(202, 151, 44); /*#CA972C*/
  --color-brand-700: rgb(241, 180, 52); /*#F1B434*/
  --color-brand-600: rgb(244, 195, 93); /*#F4C35D*/
  --color-brand-500: rgb(247, 210, 133); /*#F7D285*/
  --color-brand-400: rgb(248, 217, 153); /*#F8D999*/
  --color-brand-300: rgb(249, 225, 174); /*#F9E1AE*/
  --color-brand-200: rgb(252, 240, 214); /*#FCF0D6*/
  --color-brand-100: rgb(254, 249, 239); /*#FEF9EF*/

  --color-grey-800: rgb(34, 31, 29); /*#221F1D*/
  --color-grey-700: rgb(45, 41, 38); /*#2D2926*/
  --color-grey-600: rgb(66, 62, 60); /*#423E3C*/
  --color-grey-500: rgb(87, 84, 81); /*#575451*/
  --color-grey-400: rgb(150, 148, 147); /*#969493*/
  --color-grey-300: rgb(171, 169, 168); /*#ABA9A8*/
  --color-grey-200: rgb(213, 212, 212); /*#D5D4D4*/
  --color-grey-100: rgb(238, 238, 238); /*#EEEEEE*/
  /*--color-grey-50: rgb(245, 245, 245); !*#F5F5F5*!*/
  --color-grey-0: rgb(255, 255, 255); /*#FFFFFF*/

  --color-success-800: rgb(1, 90, 41); /*#015A29*/
  --color-success-700: rgb(1, 132, 61); /*#01843D*/
  --color-success-600: rgb(52, 157, 100); /*#349D64*/
  --color-success-500: rgb(103, 181, 139); /*#67B58B*/
  --color-success-400: rgb(153, 206, 177); /*#99CEB1*/
  --color-success-300: rgb(179, 218, 197); /*#B3DAC5*/
  --color-success-200: rgb(204, 230, 216); /*#CCE6D8*/
  --color-success-100: rgb(230, 243, 236); /*#E6F3EC*/
  --color-success-50: rgb(242, 249, 245); /*#F2F9F5*/

  --color-negative-800: rgb(141, 0, 0); /*#8D0000*/
  --color-negative-700: rgb(208, 0, 0); /*#D00000*/
  --color-negative-600: rgb(218, 51, 51); /*#DA3333*/
  --color-negative-500: rgb(227, 102, 102); /*#E36666*/
  --color-negative-400: rgb(236, 153, 153); /*#EC9999*/
  --color-negative-300: rgb(241, 178, 178); /*#F1B2B2*/
  --color-negative-200: rgb(246, 204, 204); /*#F6CCCC*/
  --color-negative-100: rgb(250, 229, 229); /*#FAE5E5*/
  --color-negative-50: rgb(253, 242, 242); /*#FDF2F2*/

  --color-warning-800: rgb(152, 84, 3); /*#985403*/
  --color-warning-700: rgb(224, 123, 4); /*#E07B04*/
  --color-warning-600: rgb(230, 149, 54); /*#E69536*/
  --color-warning-500: rgb(236, 176, 104); /*#ECB068*/
  --color-warning-400: rgb(243, 202, 155); /*#F3CA9B*/
  --color-warning-300: rgb(246, 215, 180); /*#F6D7B4*/
  --color-warning-200: rgb(249, 229, 205); /*#F9E5CD*/
  --color-warning-100: rgb(252, 242, 230); /*#FCF2E6*/
  --color-warning-50: rgb(253, 248, 242); /*#FDF8F2*/

  /* OLO waste categories */
  --color-waste-blue: rgb(0, 94, 184); /*#005EB8*/
  --color-waste-yellow: rgb(254, 219, 0); /*#FEDB00*/
  --color-waste-green: rgb(0, 156, 79); /*#009C4F*/
  --color-waste-orange: rgb(232, 144, 40); /*#E89028*/
  --color-waste-red: rgb(252, 76, 2); /*#FC4C02*/
  --color-waste-red-bratislava: rgb(215, 25, 32); /*#D71920*/
  --color-waste-brown: rgb(116, 79, 40); /*#744F28*/
  --color-waste-brown-dark: rgb(57, 49, 42); /*#392A31*/

  --color-tag-green: rgb(2, 96, 45); /*#02602D*/

  /* Tokens - border */

  --color-border-error: var(--color-negative-700);
  --color-border-success: var(--color-success-700);
  --color-border-warning: var(--color-warning-700);

  --color-border-passive-primary: var(--color-grey-200);
  --color-border-passive-secondary: var(--color-brand-400);

  --color-border-active-default: var(--color-grey-200);
  --color-border-active-hover: var(--color-grey-300);
  --color-border-active-focused: var(--color-grey-700);
  --color-border-active-pressed: var(--color-grey-800);
  --color-border-active-disabled: var(--color-grey-300);

  --color-border-active-primary-default: var(--color-brand-700);
  --color-border-active-primary-hover: var(--color-brand-600);
  --color-border-active-primary-pressed: var(--color-brand-800);
  --color-border-active-primary-inverted-default: var(--color-grey-700);
  --color-border-active-primary-inverted-hover: var(--color-grey-600);
  --color-border-active-primary-inverted-pressed: var(--color-grey-800);

  --color-border-active-secondary-default: var(--color-brand-700);
  --color-border-active-secondary-hover: var(--color-brand-600);
  --color-border-active-secondary-pressed: var(--color-brand-800);

  --color-border-active-tertiary-default: var(--color-grey-200);
  --color-border-active-tertiary-hover: var(--color-grey-300);
  --color-border-active-tertiary-pressed: var(--color-grey-400);

  --color-border-richtext-blockquote: var(--color-brand-700);
  --color-border-divider-brand: var(--color-brand-700);
  --color-border-footer-divider: var(--color-grey-600);

  /* Tokens - background */

  --color-background-error-default: var(--color-negative-700);
  --color-background-error-hover: var(--color-negative-600);
  --color-background-error-pressed: var(--color-negative-800);
  --color-background-error-soft-default: var(--color-negative-100);
  --color-background-error-soft-hover: var(--color-negative-200);
  --color-background-error-soft-pressed: var(--color-negative-300);

  --color-background-warning-default: var(--color-warning-700);
  --color-background-warning-soft-default: var(--color-warning-100);

  --color-background-success-default: var(--color-success-700);
  --color-background-success-soft-default: var(--color-success-100);

  --color-background-passive-base: var(--color-grey-0);
  --color-background-passive-primary: var(--color-grey-100);
  --color-background-passive-secondary: var(--color-brand-200);
  --color-background-passive-tertiary: var(--color-brand-700);
  --color-background-passive-inverted-base: var(--color-grey-800);

  --color-background-active-primary-default: var(--color-brand-700);
  --color-background-active-primary-hover: var(--color-brand-600);
  --color-background-active-primary-pressed: var(--color-brand-800);
  --color-background-active-primary-inverted-default: var(--color-grey-700);
  --color-background-active-primary-inverted-hover: var(--color-grey-600);
  --color-background-active-primary-inverted-pressed: var(--color-grey-800);

  --color-background-active-primary-soft-default: var(--color-transparent);
  --color-background-active-primary-soft-hover: var(--color-brand-200);
  --color-background-active-primary-soft-pressed: var(--color-brand-300);
  --color-background-active-primary-soft-inverted-default: var(--color-transparent);
  --color-background-active-primary-soft-inverted-hover: var(--color-grey-500);
  --color-background-active-primary-soft-inverted-pressed: var(--color-grey-600);

  --color-background-active-secondary-default: var(--color-transparent);
  --color-background-active-secondary-hover: var(--color-transparent);
  --color-background-active-secondary-pressed: var(--color-transparent);

  --color-background-modal: var(--color-grey-700); /* TODO opacity */
  --color-background-navmenu-trigger-hover: var(--color-grey-100);
  --color-background-select-disabled: var(--color-grey-100);
  --color-background-tag-article: var(--color-tag-green);

  --color-background-waste-paper: var(--color-waste-blue);
  --color-background-waste-plastic: var(--color-waste-yellow);
  --color-background-waste-glass: var(--color-waste-green);
  --color-background-waste-civic-amenity-site: var(--color-brand-700);
  --color-background-waste-cooking-oils-and-fats: var(--color-waste-red);
  --color-background-waste-kitchen: var(--color-waste-brown);
  --color-background-waste-garbage-bag: var(--color-waste-orange);
  --color-background-waste-organic: var(--color-waste-brown);
  --color-background-waste-mixed: var(--color-grey-700);
  --color-background-waste-cemetery: var(--color-waste-brown-dark);
  --color-background-waste-christmas-trees: var(--color-waste-red-bratislava);

  /* Tokens - content */

  --color-content-error-default: var(--color-negative-700);
  --color-content-error-hover: var(--color-negative-600);
  --color-content-error-pressed: var(--color-negative-800);
  --color-content-warning-default: var(--color-warning-700);
  --color-content-success-default: var(--color-success-700);

  --color-content-passive-primary: var(--color-grey-800);
  --color-content-passive-secondary: var(--color-grey-700);
  --color-content-passive-tertiary: var(--color-grey-600);
  --color-content-passive-inverted-primary: var(--color-grey-0);
  --color-content-passive-inverted-secondary: var(--color-grey-200);
  --color-content-passive-inverted-tertiary: var(--color-grey-400);

  --color-content-active-primary-default: var(--color-grey-0);
  --color-content-active-primary-hover: var(--color-grey-200);
  --color-content-active-primary-pressed: var(--color-grey-300);
  --color-content-active-primary-inverted-default: var(--color-grey-700);
  --color-content-active-primary-inverted-hover: var(--color-grey-600);
  --color-content-active-primary-inverted-pressed: var(--color-grey-800);

  --color-content-active-secondary-default: var(--color-grey-700);
  --color-content-active-secondary-hover: var(--color-grey-600);
  --color-content-active-secondary-pressed: var(--color-grey-800);

  --color-content-tag-article: var(--color-tag-green);
  --color-content-logo-brand: var(--color-brand-700);
  --color-content-select-disabled: var(--color-grey-500);

  /* Font - weight, size, line height */

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-black: 800;

  --text-button-large: 1rem; /* 16px */
  --leading-button-large: 1.5rem; /* 24px */
  --text-button-default: 0.875rem; /* 14px */
  --leading-button-default: 1.5rem; /* 24px */

  --text-p-large: 1.25rem; /* 20px */
  --leading-p-large: 1.75rem; /* 28px */
  --text-p-default: 1rem; /* 16px */
  --leading-p-default: 1.5rem; /* 24px */
  --text-p-small: 0.875rem; /* 14px */
  --leading-p-small: 1.25rem; /* 20px */

  --text-h6: 1rem; /* 16px */
  --leading-h6: 1.5rem; /* 24px */
  --text-h6-r: 1rem; /* 16px */
  --leading-h6-r: 1.5rem; /* 24px */

  --text-h5: 1.25rem; /* 20px */
  --leading-h5: 1.75rem; /* 28px */
  --text-h5-r: 1rem; /* 16px */
  --leading-h5-r: 1.5rem; /* 24px */

  --text-h4: 1.5rem; /* 24px */
  --leading-h4: 2rem; /* 32px */
  --text-h4-r: 1.125rem; /* 18px */
  --leading-h4-r: 1.625rem; /* 26px */

  --text-h3: 1.75rem; /* 28px */
  --leading-h3: 2.25rem; /* 36px */
  --text-h3-r: 1.25rem; /* 20px */
  --leading-h3-r: 1.75rem; /* 28px */

  --text-h2: 2rem; /* 32px */
  --leading-h2: 2.5rem; /* 40px */
  --text-h2-r: 1.5rem; /* 24px */
  --leading-h2-r: 2rem; /* 32px */

  --text-h1: 2.5rem; /* 40px */
  --leading-h1: 3rem; /* 48px */
  --text-h1-r: 1.75rem; /* 28px */
  --leading-h1-r: 2.25rem; /* 36px */

  --text-h1-hero: 3.5rem; /* 56px */
  --leading-h1-hero: 4rem; /* 64px */
  --text-h1-hero-r: 2rem; /* 32px */
  --leading-h1-hero-r: 2.5rem; /* 40px */

  /* ANIMATIONS */

  --animate-enter-from-right: enterFromRight 250ms ease;
  --animate-exit-to-right: exitToRight 250ms ease;
  --animate-fade-in: fadeIn 200ms ease;
  --animate-fade-out: fadeOut 200ms ease;
}

@layer base {
  /* FONTS */

  @font-face {
    font-family: 'Frutiger';
    font-weight: 300;
    src: url('../../public/fonts/FrutigerLTPro-Light.otf');
  }
  @font-face {
    font-family: 'Frutiger';
    font-weight: 400;
    src: url('../../public/fonts/FrutigerLTPro-Roman.otf');
  }
  @font-face {
    font-family: 'Frutiger';
    font-weight: 700;
    src: url('../../public/fonts/FrutigerLTPro-Bold.otf');
  }
  @font-face {
    font-family: 'Frutiger';
    font-weight: 800;
    src: url('../../public/fonts/FrutigerLTPro-Black.otf');
  }

  body {
    @apply text-font antialiased;
    font-family: 'Frutiger', 'Inter', sans-serif;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-black;
  }

  /* ANIMATIONS */

  @keyframes enterFromRight {
    from {
      opacity: 0;
      transform: translateX(200px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes exitToRight {
    from {
      opacity: 1;
      transform: translateX(0);
    }
    to {
      opacity: 0;
      transform: translateX(200px);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes fadeOut {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }

  /* TAILWIND OVERRIDES */

  /* button cursor is not set to pointer on default in Tailwind v4, we do it manually */
  button:not(:disabled),
  [role='button']:not(:disabled) {
    cursor: pointer;
  }
}

@layer components {
  .markdown {
    /* Nested list styles */
    & > ul {
      list-style-type: disc;

      & li > ul {
        list-style-type: circle;

        & li > ul {
          list-style-type: square;

          & li > ul {
            list-style-type: disc;

            & li > ul {
              list-style-type: circle;

              & li > ul {
                list-style-type: square;
              }
            }
          }
        }
      }
    }

    & > ol {
      list-style-type: decimal;

      & li > ol {
        list-style-type: lower-latin;

        & li > ol {
          list-style-type: lower-roman;

          & li > ol {
            list-style-type: decimal;

            & li > ol {
              list-style-type: lower-latin;

              & li > ol {
                list-style-type: lower-roman;
              }
            }
          }
        }
      }
    }

    /* Margins */

    & > *:first-child {
      @apply mt-0;
    }

    & > *:last-child {
      @apply mb-0;
    }

    & > h1,
    & > h2,
    & > h3,
    & > h4,
    & > h5,
    & > h6 {
      @apply mt-8 mb-4;
    }

    /* Apply no top margin for paragraphs that are directly after a heading */
    & > h1 + p,
    & > h2 + p,
    & > h3 + p,
    & > h4 + p,
    & > h5 + p,
    & > h6 + p {
      @apply mt-0;
    }

    & > blockquote {
      @apply my-10;
    }

    & > figure {
      @apply my-10;
    }

    /* Select paragraphs that have an adjacent ol or ul */
    & > p:has(+ ol),
    & > p:has(+ ul) {
      @apply mb-3;
    }

    /* Adjust the margin of list items */
    & > ol,
    & > ul {
      & > li {
        @apply my-3;
      }
    }

    /* Set margin for all other elements */
    & > *:not(h1, h2, h3, h4, h5, h6, ul, ol, blockquote, figure, p:has(+ ol), p:has(+ ul)) {
      @apply my-6;
    }
  }

  /* Tables - horizontal scroll fade - used by useHorizontalScrollFade.ts */
  .scroll-fade-left::before,
  .scroll-fade-right::after {
    @apply pointer-events-none absolute top-0 z-[1] block h-full w-[25px] from-content-passive-primary/25 to-transparent opacity-0 transition-opacity duration-200 ease-in-out content-[''];
  }

  .scroll-fade-left::before {
    @apply left-0 bg-gradient-to-r;
  }

  .scroll-fade-left-opaque::before {
    @apply opacity-100;
  }

  .scroll-fade-right::after {
    @apply right-0 bg-gradient-to-l;
  }

  .scroll-fade-right-opaque::after {
    @apply opacity-100;
  }

  /* Custom override for plugin @tailwindcss/typography */
  /* https://github.com/tailwindlabs/tailwindcss-typography */
  .prose {
    & :is(h1, h2, h3, h4, h5, h6) {
      color: var(--color-content-passive-primary);
    }

    & :is(div, p, span, a, li) {
      color: var(--color-content-passive-secondary);
    }

    & li::marker {
      color: var(--color-content-passive-secondary) !important;
    }
  }
}

/* UTILITIES - text */

@utility text-button {
  @apply text-size-button-default lg:text-size-button-large;
}

@utility text-size-button-large {
  @apply text-button-large leading-button-large;
}

@utility text-size-button-default {
  @apply text-button-default leading-button-default;
}

@utility text-size-p-large {
  @apply text-p-large leading-p-large;
}

@utility text-size-p-default {
  @apply text-p-default leading-p-default;
}

@utility text-size-p-small {
  @apply text-p-small leading-p-small;
}

@utility text-size-h6 {
  @apply text-h6 leading-h6;
}

@utility text-size-h6-r {
  @apply text-h6-r leading-h6-r;
}

@utility text-size-h5 {
  @apply text-h5 leading-h5;
}

@utility text-size-h5-r {
  @apply text-h5-r leading-h5-r;
}

@utility text-size-h4 {
  @apply text-h4 leading-h4;
}

@utility text-size-h4-r {
  @apply text-h4-r leading-h4-r;
}

@utility text-size-h3 {
  @apply text-h3 leading-h3;
}

@utility text-size-h3-r {
  @apply text-h3-r leading-h3-r;
}

@utility text-size-h2 {
  @apply text-h2 leading-h2;
}

@utility text-size-h2-r {
  @apply text-h2-r leading-h2-r;
}

@utility text-size-h1 {
  @apply text-h1 leading-h1;
}

@utility text-size-h1-r {
  @apply text-h1-r leading-h1-r;
}

@utility text-size-h1-hero {
  @apply text-h1-hero leading-h1-hero;
}

@utility text-size-h1-hero-r {
  @apply text-h1-hero-r leading-h1-hero-r;
}

/* UTILITIES - other */

@utility negative-x-spacing {
  @apply -mx-4 px-4;
}

@utility chevron-down-animate {
  @apply transform transition duration-300 ease-in-out;

  .rotate-menu-trigger[data-state='open'] > & {
    @apply rotate-180;
  }
}

@utility rotate-menu-trigger {
  &[data-state='open'] > .chevron-down-animate {
    @apply rotate-180;
  }
}

/* NATIVE STYLE OVERRIDES */

/* Remove native Safari arrows in accordions  */
details > summary {
  list-style: none;
}

details > summary::-webkit-details-marker {
  display: none;
}

/* Remove arrows from input type='number' */
input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  /* Chrome, Safari, Edge, Opera */
  -webkit-appearance: none;
  margin: 0;
}

input[type='number'] {
  /* Firefox */
  -moz-appearance: textfield;
  appearance: textfield;
}

/**
 * Remove X button and decorations in native search input
 * https://github.com/tailwindlabs/tailwindcss/discussions/10190#discussioncomment-4994363
 *
 * Similar styles are used also in RAC example styling
 * https://react-spectrum.adobe.com/react-aria/SearchField.html#example
 */
input[type='search']::-webkit-search-decoration,
input[type='search']::-webkit-search-cancel-button,
input[type='search']::-webkit-search-results-button,
input[type='search']::-webkit-search-results-decoration {
  display: none;
}
