fragment PickupDayEntity on PickupDayEntity {
  id
  attributes {
    registrationNumber
    address
    frequency
    wasteType
    validFrom
    validTo
    region
    frequencySeason
  }
}

query PickupDaysByAddress($address: String!) {
  pickupDays(filters: { address: { eq: $address } }) {
    data {
      ...PickupDayEntity
    }
  }
}

query PickupDaysByRegistrationNumber($registrationNumber: String!) {
  pickupDays(filters: { registrationNumber: { eq: $registrationNumber } }) {
    data {
      ...PickupDayEntity
    }
  }
}
