import { readFile, utils } from 'xlsx'
import * as assert from 'assert'

export const parsePickupDaysXlsx = (filePath: string, importId: string) => {
  const workBook = readFile(filePath)
  // Only the first sheet is used.
  const sheet = workBook.Sheets[workBook.SheetNames[0]]
  const data = utils.sheet_to_json(sheet, {
    header: 1,
    raw: false,
  }) as unknown[][]

  // Verifying the header is the best way to check whether the file uploaded is in a format we need.
  const header = data.slice(0, 1)
  const expectedHeader = [
    [
      'Zmluva - ev. číslo',
      'Obdobie',
      'Mestská časť - AKU',
      'Ulica - AKU',
      '<PERSON><PERSON>lo popisné - AKU',
      '<PERSON><PERSON>lo orientačné - AKU',
      'Frekvencia obsluhy',
      'T1',
      'T2',
      'T3',
      'T4',
      'Typ odpadu',
      'Platnosť od',
      '<PERSON>latnos<PERSON> do',
      '<PERSON><PERSON>',
      'Druh ceny',
      'Obec - AKU',
      'Mestský obvod - AKU',
      '<PERSON><PERSON><PERSON>nos<PERSON> frekvencie'
    ],
  ]
  assert.deepStrictEqual(
    header,
    expectedHeader,
    `<PERSON>lavička zošitu sa nezhoduje.\nOčakávaná hlavička: ${JSON.stringify(
      expectedHeader,
    )}\nPrijatá hlavička: ${JSON.stringify(header)}`,
  )

  const dataWithoutHeader = data.slice(1)
  return dataWithoutHeader
    .filter((row) => row.length !== 0 /* Filter empty rows */)
    .map((row, index) => {
      const [registrationNumber, actual, cityPart, address, buildingNumber, addressNumber, frequency, T1, T2, T3, T4, wasteType, validFrom, validTo, region, priceType, city, cityPartNumber, frequencySeason] =
        row.map(String)
    return {
        registrationNumber,
        address,
        addressNumber,
        frequency,
        wasteType, 
        validFrom, 
        validTo, 
        region,
        frequencySeason,
        importId,
      }
    })
}
