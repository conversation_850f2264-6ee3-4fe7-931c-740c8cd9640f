{"name": "olo-next", "version": "0.1.0", "private": true, "license": "EUPL-1.2", "scripts": {"dev": "next dev", "develop": "npm run dev", "build": "next build", "postbuild": "npm run sitemap-gen", "start": "next start", "typecheck": "tsc", "lint": "eslint src", "lint:quiet": "npm run lint --quiet", "lint:fix": "npm run lint --fix", "prettier": "prettier --write .", "gen": "graphql-codegen && prettier --write src/services/graphql/api.ts", "openapi-gen": "dotenv -- cross-var openapi-generator-cli generate -i %NALGOO_URL%?api_key=%NALGOO_API_KEY% -g typescript-axios -o ./src/services/openapi-nalgoo", "sitemap-gen": "tsc --p tsconfig.sitemap.json && tsc-alias -p tsconfig.sitemap.json && next-sitemap --config next-sitemap.config.cjs", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "parse-translations": "i18next", "update-i18next": "npm run add next-i18next@latest react-i18next@latest i18next@latest -E && npm run add eslint-plugin-i18next@latest -D -E", "update-next": "npm run add next@latest react@latest react-dom@latest sharp@latest -E && npm run add @types/react@latest @types/react-dom@latest eslint-config-next@latest typescript@latest -D -E", "update-react-aria": "npm run add @internationalized/date@latest react-aria@latest react-aria-components@latest react-stately@latest tailwindcss-react-aria-components -E", "update-tailwind": "npm run add clsx@latest tailwind-merge@latest tailwind-scrollbar-hide@latest -E && npm run add tailwindcss@latest autoprefixer@latest postcss@latest prettier-plugin-tailwindcss@latest eslint-plugin-tailwindcss@latest -D -E"}, "dependencies": {"@bratislava/component-library": "file:.yalc/@bratislava/component-library", "@graphql-codegen/cli": "^5.0.2", "@iframe-resizer/react": "5.3.2", "@internationalized/date": "3.5.5", "@next/eslint-plugin-next": "15.3.0", "@next/third-parties": "15.3.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@sindresorhus/slugify": "^2.2.1", "@strapi/blocks-react-renderer": "^1.0.1", "@tanstack/react-query": "^5.40.1", "clsx": "2.1.1", "color2k": "^2.0.3", "cross-var": "^1.1.0", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "dotenv-cli": "^7.4.2", "eslint-plugin-i18next": "6.0.9", "eslint-plugin-lodash": "7.0.0", "eslint-plugin-lodash-fp": "2.2.0-a1", "focus-trap-react": "^10.2.3", "framer-motion": "^11.1.7", "graphql": "^16.8.1", "graphql-request": "^6.1.0", "graphql-tag": "^2.12.6", "html-react-parser": "^5.1.16", "i18next": "23.14.0", "init": "^0.1.2", "lodash": "^4.17.21", "mapbox-gl": "^3.6.0", "meilisearch": "0.48.2", "next": "15.3.0", "next-i18next": "15.3.1", "next-plausible": "^3.12.2", "next-query-params": "^5.0.0", "next-sitemap": "^4.2.3", "pretty-bytes": "^6.1.1", "react": "19.1.0", "react-aria": "3.34.3", "react-aria-components": "1.3.3", "react-dom": "19.1.0", "react-i18next": "15.0.1", "react-map-gl": "^7.1.7", "react-markdown": "10.1.0", "react-resize-detector": "^12.0.2", "react-select": "^5.8.2", "react-stately": "3.32.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "remark-supersub": "^1.0.0", "remark-unwrap-images": "^4.0.0", "sass": "^1.77.5", "sharp": "0.32.6", "storybook": "^8.0.6", "string-replace-async": "^3.0.2", "tailwind-merge": "2.5.2", "tailwind-scrollbar-hide": "1.1.7", "tailwindcss-animate": "^1.0.7", "tailwindcss-react-aria-components": "2.0.0", "use-query-params": "^2.2.1", "usehooks-ts": "^3.1.0", "xml2js": "^0.6.2"}, "devDependencies": {"@chromatic-com/storybook": "1.3.1", "@graphql-codegen/typescript": "^4.0.6", "@graphql-codegen/typescript-graphql-request": "^6.2.0", "@graphql-codegen/typescript-operations": "^4.2.0", "@openapitools/openapi-generator-cli": "^2.13.9", "@storybook/addon-essentials": "^8.0.6", "@storybook/addon-interactions": "^8.0.6", "@storybook/addon-links": "^8.0.6", "@storybook/addon-onboarding": "^8.0.6", "@storybook/blocks": "^8.0.6", "@storybook/nextjs": "^8.0.6", "@storybook/preview-api": "^8.0.9", "@storybook/react": "^8.0.6", "@storybook/test": "^8.0.6", "@svgr/webpack": "8.1.0", "@tailwindcss/postcss": "^4.1.1", "@tailwindcss/typography": "^0.5.15", "@tanstack/eslint-plugin-query": "5.67.2", "@types/dompurify": "^3.0.5", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "19.1.2", "@types/react-dom": "19.1.2", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^6.20.0", "eslint": "^8", "eslint-config-adjunct": "^4.12.2", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-auto": "0.9.0", "eslint-config-next": "15.3.0", "eslint-config-prettier": "10.0.2", "eslint-plugin-array-func": "^4.0.0", "eslint-plugin-chai-expect": "^3.0.0", "eslint-plugin-chai-friendly": "^0.7.4", "eslint-plugin-const-case": "^1.2.2", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-jest-dom": "^5.2.0", "eslint-plugin-json": "^3.1.0", "eslint-plugin-markdown": "^3.0.1", "eslint-plugin-no-constructor-bind": "^2.0.4", "eslint-plugin-no-secrets": "^0.8.9", "eslint-plugin-no-unsanitized": "^4.0.2", "eslint-plugin-no-use-extend-native": "^0.5.0", "eslint-plugin-optimize-regex": "^1.2.1", "eslint-plugin-pii": "^1.0.2", "eslint-plugin-prettier": "5.2.4", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-scanjs-rules": "^0.2.1", "eslint-plugin-security": "^2.1.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-sonarjs": "^0.23.0", "eslint-plugin-storybook": "^0.8.0", "eslint-plugin-switch-case": "^1.1.2", "eslint-plugin-testing-library": "^6.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-plugin-xss": "^0.1.12", "husky": "^9.1.7", "i18next-http-backend": "^2.5.1", "i18next-parser": "^9.1.0", "postcss": "8.4.41", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.1", "tsc-alias": "^1.8.10", "typescript": "5.5.4"}, "engines": {"node": ">=20.19.x", "npm": ">=10.8.x"}, "volta": {"node": "20.19.2", "npm": "10.8.3"}}