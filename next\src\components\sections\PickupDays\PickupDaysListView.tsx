import { useQuery } from '@tanstack/react-query'
import { useTranslation } from 'next-i18next'
import { Fragment } from 'react'

import Markdown from '@/src/components/formatting/Markdown'
import AccordionGroup from '@/src/components/lib/Accordion/AccordionGroup'
import OloIcon from '@/src/components/lib/Icon/OloIcon'
import Typography from '@/src/components/lib/Typography/Typography'
import PickupDaysIcon from '@/src/components/sections/PickupDays/PickupDaysIcon'
import {
  filterAndSortPickupDaysData,
  getNearestPickupDays,
  getNextPickupDaysInfo,
  groupPickupDays,
} from '@/src/components/sections/PickupDays/utils'
import { client } from '@/src/services/graphql'
import { PickupDaysSectionFragment } from '@/src/services/graphql/api'
import cn from '@/src/utils/cn'
import { formatDate } from '@/src/utils/formatDate'
import { formatDateLong } from '@/src/utils/formatDateLong'
import { isDefined } from '@/src/utils/isDefined'

type Props = {
  pickedValue: string
  section: PickupDaysSectionFragment
  searchType: 'address' | 'registrationNumber'
}

/**
 * Figma: TODO
 */

const PickupDaysListView = ({ pickedValue, searchType, section }: Props) => {
  const { resultsTitle } = section
  const { t } = useTranslation()
  const { data: pickupDaysData } = useQuery({
    queryKey: ['PickupDays', pickedValue, searchType],
    queryFn: () => {
      return searchType === 'address'
        ? client.PickupDaysByAddress({ address: pickedValue })
        : client.PickupDaysByRegistrationNumber({ registrationNumber: pickedValue })
    },
  })

  const wasteTypes = section.wasteTypes?.filter(isDefined) ?? []

  // mapping of wasteTypes for better sorting and filtering and to show only relevant waste types
  const wasteTypesOptions = wasteTypes
    .map((wasteTypeItem) => {
      return wasteTypeItem?.wasteType.trim()
    })
    // eslint-disable-next-line unicorn/no-array-callback-reference
    .filter(isDefined)

  const filteredPickupDays = filterAndSortPickupDaysData(pickupDaysData, wasteTypesOptions) ?? []
  const groupedPickupDays = groupPickupDays(filteredPickupDays, searchType)

  return (
    <div className="flex flex-col gap-6 py-2">
      <div className="flex flex-col gap-4">
        {resultsTitle ? <Typography variant="h2">{resultsTitle}</Typography> : null}
        <div className="flex gap-2">
          {searchType === 'registrationNumber' ? (
            <OloIcon name="assignment" />
          ) : (
            <OloIcon name="place" />
          )}
          <Typography variant="p-default">{pickedValue}</Typography>
        </div>
      </div>
      {Object.keys(groupedPickupDays).map((itemKey) => (
        <Fragment key={itemKey}>
          <div className="flex gap-2">
            {searchType === 'registrationNumber' ? (
              <OloIcon name="place" />
            ) : (
              <OloIcon name="assignment" />
            )}
            <Typography variant="p-default">
              {/* eslint-disable-next-line no-secrets/no-secrets */}
              {itemKey || t('pickupDays.noRegistrationNumber')}
            </Typography>
          </div>
          {groupedPickupDays[itemKey]?.length ? (
            <AccordionGroup
              className={cn('px-5 py-2')}
              accordionData={groupedPickupDays[itemKey]
                .map((pickupDay) => {
                  const foundWasteType = wasteTypes.find(
                    (wasteTypeItem) => pickupDay.attributes?.wasteType === wasteTypeItem.wasteType,
                  )
                  if (!foundWasteType || !pickupDay.attributes) return null
                  const { iconType, wasteTitle } = foundWasteType
                  const nextPickupDays = getNextPickupDaysInfo(pickupDay)

                  return {
                    title: wasteTitle ?? '',
                    icon: <PickupDaysIcon wasteType={iconType ?? 'mixed'} />,
                    subtitle: (
                      <div className="flex gap-2">
                        <Typography variant="p-default">{t('pickupDays.nearestPickup')}</Typography>
                        <Typography variant="p-default-bold">
                          {getNearestPickupDays(nextPickupDays, 0, 1)
                            .map((nearestDate) => formatDateLong(nearestDate))
                            .join(', ')}
                        </Typography>
                        <Typography variant="p-default">
                          {`${t('pickupDays.nextNearestPickups')} ${getNearestPickupDays(
                            nextPickupDays,
                            1,
                            3,
                          )
                            .map((nearestDate) => formatDateLong(nearestDate))
                            .join(', ')}`}
                        </Typography>
                      </div>
                    ),
                    innerClassName: 'py-4 border-none',
                    children: (
                      <Markdown
                        content={`${t('pickupDays.nextPickups')} ${nextPickupDays.map((date) => formatDate(date.toISOString())).join(', ')}`}
                      />
                    ),
                  }
                })
                // eslint-disable-next-line unicorn/no-array-callback-reference
                .filter(isDefined)}
            />
          ) : (
            <Typography variant="p-default">{t('pickupDays.noMorePickupDates')}</Typography>
          )}
        </Fragment>
      ))}
    </div>
  )
}

export default PickupDaysListView
