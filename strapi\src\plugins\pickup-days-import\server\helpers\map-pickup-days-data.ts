type PickupDaysTableData = {
    registrationNumber: string;
    address: string;
    addressNumber: string;
    frequency: string;
    wasteType: string;
    validFrom: string;
    validTo: string;
    region: string;
    frequencySeason: string;
    importId: string;
}

/**
* 
* @param frequency - expected format: BR1 [-,5];BR1 [-,5,-,-] or BR1 [-,5] OR BR1 [-,5,-,-]
* @returns - expected format [-,5];[-,5,-,-] or [-,5] OR [-,5,-,-]
*/
const getFrequency = (frequency: string) => { 
    const matchedFrequency = frequency.match(/\[[^\]]*\]/g);
    return matchedFrequency ? matchedFrequency.join(';') : '';
}

export const mapPickupDaysData = (pickupDays: PickupDaysTableData[]) => { 
    return pickupDays.map((pickupDay) => {
        return {
            registrationNumber: pickupDay.registrationNumber,
            address: `${pickupDay.address}${pickupDay.addressNumber ? ` ${pickupDay.addressNumber}` : ''}`,
            frequency: getFrequency(pickupDay.frequency),
            wasteType: pickupDay.wasteType,
            validFrom: pickupDay.validFrom,
            validTo: pickupDay.validTo,
            region: pickupDay.region,
            frequencySeason: pickupDay.frequencySeason,
            importId: pickupDay.importId,
        }
    })
}
