import { useTranslation } from 'next-i18next'

import SectionContainer from '@/src/components/layout/Section/SectionContainer'
import SectionHeader from '@/src/components/layout/Section/SectionHeader'
import PickupDaysTabs from '@/src/components/sections/PickupDays/PickupDaysTabs'
import { PickupDaysSectionFragment } from '@/src/services/graphql/api'

type Props = {
  section: PickupDaysSectionFragment
}

/**
 * Figma: TODO
 */

const PickupDaysSection = ({ section }: Props) => {
  const { title, text } = section
  const { t } = useTranslation()
  const tabs: { searchType: 'address' | 'registrationNumber'; title: string }[] = [
    {
      searchType: 'address',
      title: t('pickupDays.address'),
    },
    {
      searchType: 'registrationNumber',
      title: t('pickupDays.registrationNumber'),
    },
  ]

  return (
    <SectionContainer background="primary">
      <div className="flex flex-col gap-6 lg:gap-8">
        <SectionHeader title={title} text={text} asRichtext isFullWidth />
        {/* TODO: use meilisearch Matches Position functionality https://www.meilisearch.com/docs/reference/api/search#show-matches-position 
            already started in https://github.com/bratislava/olo.sk/tree/1214-wastecollectiondays-search-by-registration-number
        */}
        <PickupDaysTabs section={section} tabs={tabs} />
      </div>
    </SectionContainer>
  )
}

export default PickupDaysSection
