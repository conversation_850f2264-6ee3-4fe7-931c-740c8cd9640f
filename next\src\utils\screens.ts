/* As of tailwind v4, screen breakpoint values are defined in globals.css.
 * To access them, we need to compute them at runtime.
 */

const parseBreakpointValue = (unparsedValue: string): number | undefined => {
  // px
  if (/^(\d+)px$/.test(unparsedValue)) {
    return parseInt(unparsedValue.slice(0, -2), 10)
  }

  // rem
  if (/^(\d+)rem/.test(unparsedValue)) {
    return parseInt(unparsedValue.slice(0, -3), 10) * 16
  }

  return undefined
}

const getScreens = () => {
  const screens: Record<'sm' | 'md' | 'lg' | 'xl', number | undefined> = {
    sm: undefined,
    md: undefined,
    lg: undefined,
    xl: undefined,
  }

  // avoid calling getComputedStyle on server
  if (typeof window === 'undefined') return screens

  const documentStyle = getComputedStyle(document.documentElement)

  // property names should match the CSS variable names in globals.css
  screens.sm = parseBreakpointValue(documentStyle.getPropertyValue('--breakpoint-sm'))
  screens.md = parseBreakpointValue(documentStyle.getPropertyValue('--breakpoint-md'))
  screens.lg = parseBreakpointValue(documentStyle.getPropertyValue('--breakpoint-lg'))
  screens.xl = parseBreakpointValue(documentStyle.getPropertyValue('--breakpoint-xl'))

  return screens
}

export const screens = getScreens()
