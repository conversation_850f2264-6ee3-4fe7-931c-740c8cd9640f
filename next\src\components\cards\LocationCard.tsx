import { useTranslation } from 'next-i18next'
import { useId } from 'react'

import Button from '@/src/components/lib/Button/Button'
import CardBase from '@/src/components/lib/CardBase/CardBase'
import OloIcon, { OloIconName } from '@/src/components/lib/Icon/OloIcon'
import Typography from '@/src/components/lib/Typography/Typography'

type LocationCardProps = {
  title: string
  address?: string | null | undefined
  linkHref: string
  iconName?: OloIconName
  hasWhiteBackground?: boolean
  className?: string
}

/**
 * Figma: https://www.figma.com/file/2qF09hDT9QNcpdztVMNAY4/OLO-Web?type=design&node-id=2094-18340&mode=dev
 */

const LocationCard = ({
  title,
  linkHref,
  address,
  iconName = 'place',
  hasWhiteBackground = true,
  className,
}: LocationCardProps) => {
  const { t } = useTranslation()
  const titleId = useId()

  return (
    <CardBase
      variant="background-white"
      hasWhiteSectionBackground={hasWhiteBackground}
      className={className}
    >
      <div className="flex h-full flex-col justify-between gap-6 p-6">
        <div className="flex flex-col items-start gap-6">
          <div className="rounded-full bg-background-passive-secondary p-4">
            <OloIcon name={iconName} className="size-6 text-background-passive-tertiary" />
          </div>
          <div className="flex flex-col gap-2 self-stretch lg:gap-3">
            <Typography
              id={titleId}
              variant="h5"
              as="h3"
              className_onlyWhenNecessary="group-hover/CardBase:underline"
            >
              {title}
            </Typography>
            {address ? <Typography variant="p-default">{address}</Typography> : null}
          </div>
        </div>
        <Button
          variant="category-outline"
          href={linkHref}
          asLink
          stretched
          fullWidth
          aria-labelledby={titleId}
        >
          {t('common.findOutMore')}
        </Button>
      </div>
    </CardBase>
  )
}

export default LocationCard
