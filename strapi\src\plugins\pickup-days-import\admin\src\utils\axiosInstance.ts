import axios from 'axios'
import { auth } from '@strapi/helper-plugin'

/**
 *
 * Copied from Marianum https://github.com/bratislava/marianum.sk/blob/72dc36cfb05d544680ef891d633af9cf59f5b9e5/strapi/src/plugins/ceremonies-debtor-list/admin/src/utils/axiosInstance.ts
 *
 * In Marianum, it says this file was generated by Strapi plugin generator.
 * But in this project, it was not generated automatically, so we copied it from <PERSON><PERSON>.
 *
 */

const instance = axios.create({
  baseURL: process.env.STRAPI_ADMIN_BACKEND_URL,
  timeout: 300000 // 5 minutes timeout
})

instance.interceptors.request.use(
  async (config) => {
    config.headers.Authorization = `Bearer ${auth.getToken()}`

    return config
  },
  (error) => {
    Promise.reject(error)
  },
)

instance.interceptors.response.use(
  (response) => response,
  (error) => {
    // whatever you want to do with the error
    if (error.response?.status === 401) {
      auth.clearAppStorage()
      window.location.reload()
    }

    throw error
  },
)

export default instance
