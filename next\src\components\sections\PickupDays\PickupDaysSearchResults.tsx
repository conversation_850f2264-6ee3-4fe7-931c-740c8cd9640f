import { Fragment, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { useEventListener } from 'usehooks-ts'

import Button from '@/src/components/lib/Button/Button'
import Divider from '@/src/components/lib/Divider/Divider'
import Spinner from '@/src/components/lib/Spinner/Spinner'
import Typography from '@/src/components/lib/Typography/Typography'

type PickupDaysSearchResultsProps = {
  searchResults: string[]
  isPending: boolean
  error: Error | null
  handleSearchItemClick: (address: string) => void
  focusId: string
}

const PickupDaysSearchResults = ({
  searchResults,
  isPending,
  error,
  handleSearchItemClick,
  focusId,
}: PickupDaysSearchResultsProps) => {
  const { t } = useTranslation()
  const inputRef = useRef<(HTMLAnchorElement | HTMLButtonElement | null)[]>([])

  useEventListener('keydown', (element) => {
    if (element.key !== 'ArrowDown' && element.key !== 'ArrowUp') {
      return
    }
    // eslint-disable-next-line xss/no-mixed-html
    const index = inputRef.current.indexOf(element.target as HTMLButtonElement)
    if (index === -1) {
      return
    }
    if (element.key === 'ArrowDown' && index < inputRef.current.length - 1) {
      inputRef.current[index + 1]?.focus()
    }
    if (element.key === 'ArrowUp' && index > 0) {
      inputRef.current[index - 1]?.focus()
    }
    if (element.key === 'ArrowUp' && index === 0) {
      document.querySelector<HTMLInputElement>('#pickup-days-search-field')?.focus()
    }
    element.preventDefault()
  })

  if (error)
    return (
      <div className="flex flex-col p-4">
        <Typography variant="p-default">{error?.message}</Typography>
      </div>
    )

  if (isPending)
    return (
      <div className="flex flex-col items-center justify-center p-10">
        <Spinner size="medium" />
      </div>
    )

  return searchResults?.length ? (
    <div className="flex flex-col">
      {searchResults.map((searchResult, index) => (
        <Fragment key={searchResult}>
          <Button
            variant="unstyled"
            onPress={() => handleSearchItemClick(searchResult)}
            // TODO: implement token colors
            className="flex items-center gap-2 self-stretch px-5 py-3 ring-offset-[-2px] hover:bg-gray-100"
            ref={(element) => {
              inputRef.current[index] = element
            }}
            id={index === 0 ? focusId : undefined}
          >
            {searchResult}
          </Button>
          <Divider className="mx-5 h-0.5 w-auto last:hidden" />
        </Fragment>
      ))}
    </div>
  ) : (
    <div className="flex flex-col">
      <div className="px-4 py-2">{t('globalSearch.noResults')}</div>
    </div>
  )
}

export default PickupDaysSearchResults
