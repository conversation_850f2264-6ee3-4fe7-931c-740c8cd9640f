import { PickupDayEntityFragment } from '@/src/services/graphql/api'

import { meiliClient } from '../meiliClient'
import { PickupDayMeili, SearchIndexWrapped } from '../types'
import { getMeilisearchPageOptions, unwrapFromSearchIndex } from '../utils'

export type PickupDaysFilters = {
  search: string
  page: number
  pageSize: number
}

export const pickupDaysDefaultFilters: PickupDaysFilters = {
  search: '',
  page: 1,
  pageSize: 10,
}

export const getMeiliPickupDaysQueryKey = (filters: PickupDaysFilters, wasteTypes: string[]) => [
  'PickupDays',
  filters,
  wasteTypes,
]

export const meiliPickupDaysFetcher = (filters: PickupDaysFilters, wasteTypes: string[]) => {
  const wasteTypeFilter = wasteTypes.map((type) => `pickup-day.wasteType = "${type}"`).join(' OR ')

  return meiliClient
    .index('pickup-day')
    .search<SearchIndexWrapped<'pickup-day', PickupDayMeili>>(filters.search, {
      ...getMeilisearchPageOptions({ page: filters.page, pageSize: filters.pageSize }),
      filter: ['type = "pickup-day"', wasteTypeFilter],
      sort: ['pickup-day.address:asc'],
    })
    .then(unwrapFromSearchIndex('pickup-day'))
    .then((response) => {
      const hits: PickupDayEntityFragment[] = response.hits.map((hit) => {
        return {
          __typename: 'PickupDayEntity',
          id: hit.id,
          attributes: {
            __typename: 'PickupDay',
            ...hit,
          },
        }
      })

      return { ...response, hits }
    })
}
