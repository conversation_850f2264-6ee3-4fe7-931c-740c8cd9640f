{"name": "@bratislava/component-library", "version": "1.0.4", "main": "dist/main.js", "types": "dist/lib/main.d.ts", "type": "module", "files": ["dist"], "scripts": {"dev": "vite", "build": "tsc --p ./tsconfig-build.json && vite build", "release": "npm run build && npm publish", "preview": "vite preview", "lint": "eslint lib src", "lint:fix": "eslint --fix lib src", "prettier": "prettier --write .", "prettier:check": "prettier --check ."}, "dependencies": {"react-aria": "^3.29.0", "react-aria-components": "^1.0.0-beta.2"}, "peerDependencies": {"react": "^18.3.1", "react-dom": "^18.3.1"}, "yalcSig": "9cf5d9dbe559a644f39663da52250e0d"}