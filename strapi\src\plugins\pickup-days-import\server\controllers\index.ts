import { Strapi } from '@strapi/strapi'
import { parsePickupDaysXlsx } from '../helpers/parse-pickup-days-xlsx'
import { v4 as uuid } from 'uuid'
import { mapPickupDaysData } from '../helpers/map-pickup-days-data'

export default {
  importXlsxController: ({ strapi }: { strapi: Strapi }) => {
    const meilisearch = strapi.plugin('meilisearch').service('meilisearch')

    const updateMeilisearchIndex = async () => {
      await meilisearch.updateContentTypeInMeiliSearch({
        contentType: 'api::pickup-day.pickup-day',
      })
    }

    const deletePickupDaysFn = async (wasteType?: string) => {
      await strapi.db?.query('api::pickup-day.pickup-day').deleteMany({
        where: {
          wasteType: wasteType,
        },
      }) 
      // TODO this approach may not be needed anymore, as we us createMany instead of creating entries one by one
      // `deleteMany` doesn't trigger Meilisearch hooks, so the old entries stay in its database,
      // also having <PERSON><PERSON><PERSON><PERSON> on while adding entries triggers the update content hook after
      // every query, therefore the best solution is to turn the Meilisearch off while adding new entries
      // and turn it back on afterward.
      // See `strapi/patches/strapi-plugin-meilisearch+0.9.2.patch`.

      // await updateMeilisearchIndex() // TODO temp do not update
    }
    
    const deleteAllPickupDaysFn = async () => {
      await strapi.db?.query('api::pickup-day.pickup-day').deleteMany() 
      // await updateMeilisearchIndex() // TODO temp do not update
    }

    return {
      async importPickupDays(ctx) {
        const start = Date.now()

        ctx.request.socket.setTimeout(300000) // 5 minutes

        const file = ctx.request.files?.file
        if (!file) {
          ctx.status = 400
          ctx.body = {
            message: 'Chýba súbor.',
          }
          return
        }

        try {
          const importId = uuid()
          const parsedPickupDays = parsePickupDaysXlsx(file.path, importId)

          try {
            // Using Query Engine API because it supports bulk insert
            // Docs: https://docs-v4.strapi.io/dev-docs/api/query-engine/bulk-operations
            //
            // We had to split the data into chunks because of some error with around 9400 items per query
            // TODO investigate the error and remove the chunking
            const chunkSize = 5000
            for (let i = 0; i < parsedPickupDays.length; i += chunkSize) {
              const chunk = parsedPickupDays.slice(i, i + chunkSize)
              await strapi.db?.query('api::pickup-day.pickup-day').createMany({
                data: mapPickupDaysData(chunk),
              })
            }

            // await updateMeilisearchIndex() // TODO temp do not update
          } catch (createError) {
            throw createError
          }

          ctx.body = {
            message: `Nahraných ${parsedPickupDays.length} odvozových dní.`,
            importId,
            executionTime: Date.now() - start,
          }
        } catch (e) {
          ctx.status = 400
          ctx.body = {
            message: e.toString(),
          }
        }
      },
      async deletePickupDays(ctx) {
        const { wasteType } = ctx.request.params

        try {
          await deletePickupDaysFn(wasteType)

          ctx.body = {
            message: `Odvozové dni "${wasteType}" boli odstránené.`,
          }
        } catch (e) {
          ctx.status = 400
          ctx.body = {
            message: e.toString(),
          }
        }
      },
      async deleteAllPickupDays(ctx) {
        const { type } = ctx.request.params

        try {
          await deleteAllPickupDaysFn()

          ctx.body = {
            message: `Odvozové dni boli odstránené.`,
          }
        } catch (e) {
          ctx.status = 400
          ctx.body = {
            message: e.toString(),
          }
        }
      },
      async updateMeilisearchPickupDays(ctx) {
        try {
          await updateMeilisearchIndex()

          ctx.body = {
            message: 'Aktualizácia odvozových dní prebehla úspešne.',
          }
        } catch (e) {
          ctx.status = 400
          ctx.body = {
            message: e.toString(),
          }
        }
      },
    }
  },
}
