{"key": "plugin_content_manager_configuration_content_types::api::pickup-day.pickup-day", "value": {"settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "address", "defaultSortBy": "registrationNumber", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "registrationNumber": {"edit": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "registrationNumber", "searchable": true, "sortable": true}}, "address": {"edit": {"label": "<PERSON><PERSON><PERSON>", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "address", "searchable": true, "sortable": true}}, "frequency": {"edit": {"label": "Frekvencia obsluhy", "description": "príklad: BR1 [5,-];BR2 [-,-,5,-] odvoz 2 sezónach, v prvej sezóne rajón BR1, nepárny týždeň piatok, druhá sezóna rajón BR2, odvoz 3. týždeň piatok", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "frequency", "searchable": true, "sortable": true}}, "wasteType": {"edit": {"label": "Typ odpadu", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "wasteType", "searchable": true, "sortable": true}}, "validFrom": {"edit": {"label": "Platnosť od", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "validFrom", "searchable": true, "sortable": true}}, "validTo": {"edit": {"label": "Platnosť do", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "validTo", "searchable": true, "sortable": true}}, "region": {"edit": {"label": "<PERSON><PERSON>", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "region", "searchable": true, "sortable": true}}, "frequencySeason": {"edit": {"label": "Sezónnosť frekvencie", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "frequencySeason", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}, "createdBy": {"edit": {"label": "created<PERSON>y", "description": "", "placeholder": "", "visible": false, "editable": true, "mainField": "firstname"}, "list": {"label": "created<PERSON>y", "searchable": true, "sortable": true}}, "updatedBy": {"edit": {"label": "updatedBy", "description": "", "placeholder": "", "visible": false, "editable": true, "mainField": "firstname"}, "list": {"label": "updatedBy", "searchable": true, "sortable": true}}}, "layouts": {"edit": [[{"name": "registrationNumber", "size": 12}], [{"name": "address", "size": 12}], [{"name": "frequency", "size": 12}], [{"name": "wasteType", "size": 12}], [{"name": "validFrom", "size": 12}], [{"name": "validTo", "size": 12}], [{"name": "region", "size": 12}], [{"name": "frequencySeason", "size": 12}]], "list": ["id", "registrationNumber", "address", "frequency"]}, "uid": "api::pickup-day.pickup-day"}, "type": "object", "environment": null, "tag": null}