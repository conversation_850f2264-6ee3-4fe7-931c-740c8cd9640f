import { useTranslation } from 'next-i18next'
import { Tab, Tab<PERSON><PERSON>, TabPanel, Tabs } from 'react-aria-components'

import PickupDaysOldDataSearch from '@/src/components/sections/PickupDaysOldData/PickupDaysOldDataSearch'
import { PickupDaysSectionFragment } from '@/src/services/graphql/api'

type PickupDaysTabsProps = {
  tabs: {
    searchType: 'address' | 'registrationNumber'
    title: string
  }[]
  section: PickupDaysSectionFragment
}

const PickupDaysOldDataTabs = ({ tabs, section }: PickupDaysTabsProps) => {
  const { t } = useTranslation()

  return (
    <Tabs className="flex flex-col gap-6">
      <TabList
        aria-label={t('pickupDays.aria.tabsListName')}
        className="negative-x-spacing -my-2 scrollbar-hide flex gap-x-4 overflow-auto overflow-y-hidden py-2"
      >
        {tabs.map((tab) => (
          <Tab
            id={tab.searchType}
            key={tab.searchType}
            className="relative cursor-pointer border-b-2 border-transparent px-4 py-3 text-center whitespace-nowrap ring-offset-2 outline-hidden focus-visible:ring-3 selected:border-border-active-pressed selected:font-semibold"
          >
            {tab.title}
          </Tab>
        ))}
      </TabList>

      {tabs.map((tab) => (
        <TabPanel id={tab.searchType} key={tab.searchType}>
          <PickupDaysOldDataSearch section={section} searchType={tab.searchType} />
        </TabPanel>
      ))}
    </Tabs>
  )
}

export default PickupDaysOldDataTabs
