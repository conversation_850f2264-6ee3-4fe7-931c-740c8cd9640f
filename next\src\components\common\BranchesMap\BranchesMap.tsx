import { motion } from 'framer-motion'
import Link from 'next/link'
import { Fragment, useRef, useState } from 'react'
import { Map, MapRef, Marker } from 'react-map-gl'

import { MapMarkerDefaultSvg, MapMarkerKoloSvg } from '@/src/assets/map-markers'
import BranchCard from '@/src/components/cards/BranchCard'
import Divider from '@/src/components/lib/Divider/Divider'
import { environment } from '@/src/environment'
import { BranchItemFragment } from '@/src/services/graphql/api'
import cn from '@/src/utils/cn'
import { getBoundsForBranches } from '@/src/utils/getBoundsForBranches'
import { isDefined } from '@/src/utils/isDefined'
import { useGetFullPath } from '@/src/utils/useGetFullPath'

export type BranchesMapProps = {
  branchItems: BranchItemFragment[]
}

/**
 * Figma: https://www.figma.com/design/2qF09hDT9QNcpdztVMNAY4/OLO-Web?node-id=1183-12850&m=dev
 */

const BranchesMap = ({ branchItems }: BranchesMapProps) => {
  const { getFullPath } = useGetFullPath()
  const [hoveredBranchId, setHoveredBranchId] = useState<string | null>(null)
  const mapRef = useRef<MapRef | null>(null)

  const initialBounds = getBoundsForBranches(branchItems)
  const mapStyle = `mapbox://styles/${environment.mapboxUsername}/${environment.mapboxStyleId}`
  // eslint-disable-next-line const-case/uppercase
  const markerClasses =
    'text-background-active-primary-default hover:text-background-active-primary-hover'

  return (
    <div className="flex flex-col items-center justify-center bg-background-passive-base lg:overflow-hidden lg:rounded-lg">
      <ul className="flex w-full flex-col justify-center gap-4 p-4 lg:flex-row lg:gap-8 lg:p-8">
        {branchItems
          .map((branchItem, index) => {
            if (!branchItem?.branch?.data?.attributes) return null
            const { title, address, image } = branchItem.branch.data.attributes

            return (
              <Fragment key={branchItem.branch.data.id}>
                {index > 0 ? (
                  <Divider variant="horizontal" className="lg:w-0 lg:border-b-0 lg:border-l" />
                ) : null}
                <li className="grow lg:w-0">
                  <BranchCard
                    title={branchItem.titleOverride ?? title}
                    linkHref={getFullPath(branchItem.branch.data)}
                    address={address}
                    imgSrc={image?.data?.attributes?.url}
                    variant="unstyled"
                    className="size-full"
                    typographyClassName={cn({
                      underline: hoveredBranchId === branchItem.branch.data.id, // Underline card title when hovering over its map marker
                    })}
                  />
                </li>
              </Fragment>
            )
          })
          // eslint-disable-next-line unicorn/no-array-callback-reference
          .filter(isDefined)}
      </ul>

      <div className="h-[30rem] w-full overflow-hidden">
        <Map
          ref={mapRef}
          style={{ width: '100%', height: '100%' }}
          initialViewState={{
            bounds: initialBounds, // TODO: Limit map bounds to Bratislava
            fitBoundsOptions: {
              padding: 100,
              offset: [0, 10],
            },
          }}
          mapboxAccessToken={environment.mapboxAccessToken}
          mapStyle={mapStyle}
          attributionControl={false}
          cooperativeGestures
        >
          {branchItems.map((branchItem) => {
            if (!branchItem?.branch?.data) return null
            const { latitude, longitude, mapIconName } = branchItem.branch.data.attributes ?? {}
            if (!latitude || !longitude) return null

            return (
              <Marker
                key={branchItem.branch.data.id}
                longitude={longitude}
                latitude={latitude}
                anchor="bottom"
              >
                <motion.button
                  style={{ originY: 1 }}
                  initial={{ scale: 0 }}
                  animate={{ scale: hoveredBranchId === branchItem.branch.data.id ? 1 : 0.75 }}
                  whileTap={{ scale: 0.8 }}
                >
                  <Link
                    href={getFullPath(branchItem.branch.data) ?? ''}
                    onMouseEnter={() => setHoveredBranchId(branchItem?.branch?.data?.id ?? null)}
                    onMouseLeave={() => setHoveredBranchId(null)}
                  >
                    {mapIconName === 'kolo' ? (
                      <MapMarkerKoloSvg className={markerClasses} />
                    ) : (
                      <MapMarkerDefaultSvg className={markerClasses} />
                    )}
                  </Link>
                </motion.button>
              </Marker>
            )
          })}
        </Map>
      </div>
    </div>
  )
}

export default BranchesMap
