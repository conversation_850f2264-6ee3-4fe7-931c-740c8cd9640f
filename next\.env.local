NEXT_PUBLIC_DEPLOYMENT=dev
NEXT_PUBLIC_NEXT_HOST=127.0.0.1:3000
NEXT_PUBLIC_SITE_URL=http://127.0.0.1:3000

## local
# NEXT_PUBLIC_STRAPI_URL=http://127.0.0.1:1337
# NEXT_PUBLIC_MEILISEARCH_HOST=http://localhost:7700
# NEXT_PUBLIC_MEILISEARCH_SEARCH_API_KEY=b4f727e3315b104b35a10954aeac768fc9b11df6ff2607cccb29812c47d6e4e3

## dev
# NEXT_PUBLIC_STRAPI_URL=https://olo-strapi.dev.bratislava.sk
# NEXT_PUBLIC_MEILISEARCH_HOST=https://olo-strapi-meilisearch.dev.bratislava.sk/
# NEXT_PUBLIC_MEILISEARCH_SEARCH_API_KEY=854dc0a9a7c27b0a6f7007a4983c144cbd465bd58e9fb2844ad81808ef2d8b00

## staging
NEXT_PUBLIC_STRAPI_URL=https://olo-strapi.staging.bratislava.sk
NEXT_PUBLIC_MEILISEARCH_HOST=https://olo-strapi-meilisearch.staging.bratislava.sk/
NEXT_PUBLIC_MEILISEARCH_SEARCH_API_KEY=9750e8be3417b30f76cae3d68f10c267cab9e9e67eb9292b762b7d4a1f8301f6 

## production
# NEXT_PUBLIC_STRAPI_URL=https://olo-strapi.bratislava.sk
# NEXT_PUBLIC_MEILISEARCH_HOST=https://olo-strapi-meilisearch.bratislava.sk/
# NEXT_PUBLIC_MEILISEARCH_SEARCH_API_KEY=ae84ae0982c2162a81eb253765ceaa8593abd9105c71954cf5c9620b0178cbb6

MINIO_BUCKET=olo
# STRAPI_REVALIDATE_SECRET_TOKEN=TODO

NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoiaW5vdmFjaWVicmF0aXNsYXZhIiwiYSI6ImNtMGwwcDExaDAwbDkyanM4emg0NnMxa2YifQ.XCP0imMoiScY0xIF88asQw
NEXT_PUBLIC_MAPBOX_USERNAME=inovaciebratislava
NEXT_PUBLIC_MAPBOX_STYLE_ID=cm0l0wpy9007j01pj7smo6y6m

# this api key needs to be added in generation script for Nalgoo when running open api generation, but it is working only from .env and not working in .env.local for this generation, so it needs to be in both .env and .env.local
# TODO: find the way to set dotenv and cross-var to accepts variables from .env.local
NALGOO_API_KEY=45UKupQXQRJ3UVjJQmGxkBJzRSPhPXxs
NALGOO_URL=https://ats.nalgoo.com/api/rest
JOSEPHINE_API_TOKEN=72k6r9gAOy149DF
JOSEPHINE_URL=https://josephine.proebiz.com/sk/api/client/webpublisher/

# Forms iframe
NEXT_PUBLIC_CITY_ACCOUNT_URL=https://konto.bratislava.sk/

# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-F1VGQS4QGD #dummy
NEXT_PUBLIC_GA_MEASUREMENT_ID_STAGING=G-F1VGQS4QGD
NEXT_PUBLIC_GA_MEASUREMENT_ID_PROD=G-RZ7Z7N8X28

MAILGUN_API_KEY=**************************************************
MAILGUN_DOMAIN=no-reply-strapi.bratislava.sk
MAILGUN_EMAIL=<EMAIL>
MAILGUN_HOST=https://api.eu.mailgun.net

NEXT_PUBLIC_GTM_ID=GTM-MKZTGCX
NEXT_PUBLIC_GTM_AUTH=hRtJKgMKi9fnugwG-v9rRw
NEXT_PUBLIC_GTM_PREVIEW=env-19
