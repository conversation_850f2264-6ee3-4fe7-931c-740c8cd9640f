import { addDays, addWeeks, isAfter, isBefore, isEqual, startOfWeek } from 'date-fns'
import groupBy from 'lodash/groupBy'

import { PickupDayEntityFragment, PickupDaysByAddressQuery } from '@/src/services/graphql/api'
import { getCurrentWeekOfYear } from '@/src/utils/getCurrentWeekOfYear'
import { isDefined } from '@/src/utils/isDefined'

type SearchType = 'address' | 'registrationNumber'

/**
 * This function filters unique addresses based on the PickupDay data
 * TODO: might be enough to use distinct attribute in meilisearch https://www.meilisearch.com/docs/learn/relevancy/distinct_attribute
 */
export const filterUniqueResultsBySearchType = (
  data: PickupDayEntityFragment[] | null | undefined,
  searchType: SearchType,
) => {
  // TODO: filter out addresses that have only type of data that we don't display, ideally when we load data to Strapi

  const searchData =
    data
      ?.map((wasteCollectionDaysItem) => {
        return searchType === 'address'
          ? wasteCollectionDaysItem?.attributes?.address?.trim()
          : wasteCollectionDaysItem?.attributes?.registrationNumber?.trim()
      })
      // eslint-disable-next-line unicorn/no-array-callback-reference
      .filter(isDefined) ?? []

  return [...new Set<string>(searchData)]
}

// Expected results:
// console.log('-,-,-,- equals =', getNormalizedFrequency('-,-'))
// console.log('-,1,-,1 equals =', getNormalizedFrequency('-,1'))
// console.log('-,123,-123 equals =', getNormalizedFrequency('-,123'))
// eslint-disable-next-line no-secrets/no-secrets
// console.log('123,456,123,456 equals =', getNormalizedFrequency('123,456'))
const getNormalizedFrequency = (frequency: string) => {
  return frequency.split(',').length === 2 // 14 days frequency, needs to be normalized to 28 days frequency
    ? `${frequency},${frequency}`
    : frequency
}

// Expected results:
// console.log('1 equals =', mergeWeeksInFrequency('1', '-'))
// console.log('12 equals =', mergeWeeksInFrequency('1', '2'))
// console.log('12 equals =', mergeWeeksInFrequency('12', '-'))
// console.log('12345 equals =', mergeWeeksInFrequency('12', '34'))
// console.log(' equals =', mergeWeeksInFrequency('-', '-'))
// console.log('1234 equals =', mergeWeeksInFrequency('12345', '-'))
// console.log('1234 equals =', mergeWeeksInFrequency('13', '24'))
// console.log('24 equals =', mergeWeeksInFrequency('-', '24'))
const mergeWeeksInFrequency = (week1: string, week2: string) => {
  // merge pickup days from two frequencies but same week into one string
  return [...new Set([...week1, ...week2])]
    .filter((day) => day !== '-' && day !== '')
    .sort()
    .join('')
}

//  Expected results:
//  console.log('1,12,1,2 equals =', getMergeFrequencyString('-,1,-,-', '1,2'))
//  console.log('1,123,1,2 equals =', getMergeFrequencyString('-,123,-,-', '1,2'))
//  console.log('1,2,1,2 equals =', getMergeFrequencyString('-,-,-,-', '1,2'))
//  console.log('12,234,345,56 equals =', getMergeFrequencyString('1,2,34,5', '2,34,5,6'))
//  console.log('24,12345,24,24 equals =', getMergeFrequencyString('-,135,-,-', '24,24'))
const getMergeFrequencyString = (region1Frequency: string, region2Frequency: string) => {
  // merge frequencies for two regions into one string
  const normalizedRegion1Frequency = getNormalizedFrequency(region1Frequency).split(',')
  const normalizedRegion2Frequency = getNormalizedFrequency(region2Frequency).split(',')

  return normalizedRegion1Frequency
    .map((region1FrequencyWeek, key) =>
      mergeWeeksInFrequency(region1FrequencyWeek, normalizedRegion2Frequency[key]),
    )
    .join(',')
}

// season in the data is in format 'dd/mm' without year, so this parsing function is needed
const parseSeasonDate = (
  monthDay: string,
  currentDate: Date,
  referenceMonth?: number,
  referenceDay?: number,
) => {
  const [day, month] = monthDay.split('/').map(Number)
  let year = currentDate.getFullYear()
  // If referenceMonth is provided and the parsed month is less than the reference, it's in the next year
  if (
    referenceMonth !== undefined &&
    (month < referenceMonth || (month === referenceMonth && day < (referenceDay ?? 1)))
  ) {
    year += 1
  }

  return new Date(year, month - 1, day)
}

// Expected results:
// console.log('[2025-02-28, 2025-11-30], [2025-12-02, 2026-02-27] equals to =', parseSeason('28/02-30/11, 01/12-27/02', new Date('2025-06-01')))
// return range from season in which current date fits
const parseSeason = (season: string, currentDate: Date) => {
  return season.split(',').map((range) => {
    const [startString, endString] = range.split('-').map((date) => date.trim())
    const [startDay, startMonth] = startString.split('/').map(Number)
    const start = parseSeasonDate(startString, currentDate)
    const end = parseSeasonDate(endString, currentDate, startMonth, startDay)

    return [start, end]
  })
}

// Expected results:
// console.log('true equals = ',isCurrentDayInSeason(parseSeason('28/02-30/11, 01/12-27/02', new Date('2025-06-01'))[0], new Date('2025-06-01')))
// console.log('true equals =',isCurrentDayInSeason(parseSeason('28/02-30/11, 01/12-27/02', new Date('2025-01-01'))[1], new Date('2026-01-01')))
// check if current date is in season range
const isCurrentDayInSeason = (seasonRange: Date[], currentDate: Date) => {
  if (seasonRange.length !== 2) return false
  const [start, end] = seasonRange
  if (isAfter(end, start) || isEqual(end, start)) {
    return (
      (isEqual(currentDate, start) || isAfter(currentDate, start)) &&
      (isEqual(currentDate, end) || isBefore(currentDate, end))
    )
  }

  return (
    isEqual(currentDate, start) ||
    isAfter(currentDate, start) ||
    isEqual(currentDate, end) ||
    isBefore(currentDate, end)
  )
}

// Expected results:
// console.log('1,2,1,2 equals to',getSeasonalFrequencyString(['1,2', '3,4'], parseSeason('28/02-30/11, 01/12-27/02', new Date('2025-06-01')), new Date('2025-06-01')),)
// console.log('3,4,3,4 equals to',getSeasonalFrequencyString(['1,2,34,56', '3,4'], parseSeason('28/02-30/11, 01/12-27/02',  new Date('2025-02-15')), new Date('2025-02-15')),)
// expected format of frequency is '12,34,-,6' for 28 days frequency, or '12,-' where '-' means no pickup day and numbers mean day of the week
// 4-week frequency format that is for current date based on seasonal changes
const getSeasonalFrequencyString = (
  splitFrequency: string[],
  parsedSeason: Date[][],
  currentDate: Date,
) => {
  if (parsedSeason?.length !== 2) return '-,-,-,-' // this format means no planned pickups
  if (splitFrequency.length < 2) return getNormalizedFrequency(splitFrequency[0])

  const [season1] = parsedSeason // we assume there are only 2 seasons in the frequency and cover whole year
  const inSeason1 = isCurrentDayInSeason(season1, currentDate)

  return inSeason1
    ? getNormalizedFrequency(splitFrequency[0])
    : getNormalizedFrequency(splitFrequency[1])
}

// Expected results:
// console.log('equals =', stripeBrackets('[]'))
// console.log('2,- equals =', stripeBrackets('[2,-]'))
// console.log('234,2 equals =', stripeBrackets('[234,2]'))
// console.log('234,2,-,1 equals =', stripeBrackets('[234,2,-,1]'))
const stripeBrackets = (frequencyWithBrackets: string) => {
  return frequencyWithBrackets.replaceAll(/[[\]]/g, '').trim()
}

// get one frequency in 28-day format based on the season and date
const getOneFrequencyString = (frequency: string, season: string, currentDate: Date) => {
  const splitFrequencies = frequency.split(';').map((freq) => stripeBrackets(freq.trim()))
  if (splitFrequencies.length < 2) return getNormalizedFrequency(splitFrequencies[0])

  if (!season) return getMergeFrequencyString(splitFrequencies[0], splitFrequencies[1])

  const parsedSeason = parseSeason(season, currentDate)

  return parsedSeason?.length === 2
    ? getSeasonalFrequencyString(splitFrequencies, parsedSeason, currentDate)
    : getMergeFrequencyString(splitFrequencies[0], splitFrequencies[1])
}

const getFirstMondayOfYear = (year: number) => {
  const jan1 = new Date(year, 0, 1)

  return startOfWeek(jan1, { weekStartsOn: 1 })
}

const getStartOfCycleDate = () => {
  const currentWeek = getCurrentWeekOfYear()
  const startOfCycleWeek = currentWeek - ((currentWeek - 1) % 4)
  const firstMonday = getFirstMondayOfYear(new Date().getFullYear())

  return addWeeks(firstMonday, startOfCycleWeek - 1)
}

const getWeekDatesFromFrequency = (weekDates: string, currentWeekStartDate: Date) => {
  return [...new Set(weekDates)]
    .filter((dayPosition) => /^\d$/.test(dayPosition)) // keep only single digits
    .map((dayPosition) => {
      const dayNumber = Number(dayPosition)

      return addDays(currentWeekStartDate, dayNumber - 1)
    })
}

const generateFourWeekDates = (startDate: Date, currentFrequency: string) => {
  return currentFrequency
    .split(',')
    .map((week) => week.trim())
    .flatMap((frequencyPerDay, index) =>
      getWeekDatesFromFrequency(frequencyPerDay, addWeeks(startDate, index)),
    )
}

export const getNearestPickupDays = (pickupDays: Date[], start: number, limit: number) => {
  if (pickupDays.length === 0) return []

  const today = new Date()
  const pickupDaysAfterToday = pickupDays.filter((date) => isAfter(date, today))

  return pickupDaysAfterToday
    .sort((dayA, dayB) => dayA.getTime() - dayB.getTime())
    .slice(start, limit)
}

export const getNextPickupDaysInfo = (pickupDay: PickupDayEntityFragment) => {
  const startOfCycle = getStartOfCycleDate()
  let currentDate = startOfCycle
  const nextPickupDays: Date[] = []

  const endDate = new Date(new Date().getFullYear() + 1, 1, 1) // Feb 1 next year
  while (isBefore(currentDate, endDate)) {
    // TODO: handle cases where season changes during the week
    const currentFrequency = getOneFrequencyString(
      pickupDay.attributes?.frequency ?? '',
      pickupDay.attributes?.frequencySeason ?? '',
      currentDate,
    )

    nextPickupDays.push(...generateFourWeekDates(currentDate, currentFrequency))
    currentDate = addWeeks(currentDate, 4)
  }

  return nextPickupDays
}

export const filterAndSortPickupDaysData = (
  pickupDaysData: PickupDaysByAddressQuery | undefined,
  wasteTypesOptions: string[],
) => {
  return pickupDaysData
    ? pickupDaysData?.pickupDays?.data
        ?.filter(isDefined)
        // TODO: implement filtering in graphql rather than in the frontend
        .filter((pickupItem) =>
          pickupItem.attributes?.wasteType
            ? wasteTypesOptions?.includes(pickupItem.attributes?.wasteType)
            : false,
        )
        // sort by address needed for registration number search
        .sort((pickupItemA, pickupItemB) => {
          return pickupItemA.attributes?.address && pickupItemB.attributes?.address
            ? pickupItemA.attributes.address.localeCompare(pickupItemB.attributes.address, 'sk')
            : 0
        })
        // sort in order how it is set in strapi
        .sort((pickupItemA, pickupItemB) => {
          return wasteTypesOptions &&
            pickupItemA.attributes?.wasteType &&
            pickupItemB.attributes?.wasteType
            ? wasteTypesOptions.indexOf(pickupItemA.attributes.wasteType) -
                wasteTypesOptions.indexOf(pickupItemB.attributes.wasteType)
            : 0
        })
    : []
}

export const groupPickupDays = (
  filteredPickupDaysData: PickupDayEntityFragment[],
  searchType: SearchType,
) => {
  const groupByAttribute = searchType === 'address' ? 'registrationNumber' : 'address'

  return groupBy(
    filteredPickupDaysData,
    (pickupDay: PickupDayEntityFragment) => pickupDay.attributes?.[groupByAttribute] ?? '',
  )
}
