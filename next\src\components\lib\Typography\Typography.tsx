import TypographyDS, { TypographyProps } from '@bratislava/component-library'
import slugify from '@sindresorhus/slugify'
import { forwardRef } from 'react'

/**
 * Figma: https://www.figma.com/file/2qF09hDT9QNcpdztVMNAY4/OLO-Web?node-id=39-2452&mode=dev
 */

// Typography from DS needs to be able to add id's for headings to create TableOfContents component
// TODO id should be passed only by prop
const Typography = forwardRef<HTMLElement, TypographyProps>(
  ({ variant = 'p-default', as, id, children, ...rest }, forwardedRef) => {
    const HEADING_REGEXP = /h\d.*/
    const isHeading = HEADING_REGEXP.test(as ? as.toString() : variant)

    const generatedId = children ? slugify(children.toString()) : ''

    return (
      <TypographyDS
        variant={variant}
        {...rest}
        ref={forwardedRef}
        id={id ?? (isHeading ? generatedId : undefined)}
      >
        {children}
      </TypographyDS>
    )
  },
)

export default Typography
