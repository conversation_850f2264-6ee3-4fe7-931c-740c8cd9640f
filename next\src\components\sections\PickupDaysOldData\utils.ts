/* eslint-disable sonarjs/no-duplicate-string */
import groupBy from 'lodash/groupBy'
import { i18n } from 'next-i18next'

import {
  WasteCollectionDayEntityFragment,
  WasteCollectionDaysByAddressQuery,
} from '@/src/services/graphql/api'
import { isCurrentWeekEven } from '@/src/utils/getCurrentWeekOfYear'
import { isDefined } from '@/src/utils/isDefined'

type SearchType = 'address' | 'registrationNumber'

/**
 * This function filters unique addresses based on the WasteCollectionDay data
 * TODO: might be enough to use distinct attribute in meilisearch https://www.meilisearch.com/docs/learn/relevancy/distinct_attribute
 */
export const filterUniqueResultsBySearchType = (
  data: WasteCollectionDayEntityFragment[] | null | undefined,
  searchType: SearchType,
) => {
  // TODO: filter out addresses that have only type of data that we don't display, ideally when we load data to Strapi

  const searchData =
    data
      ?.map((wasteCollectionDaysItem) => {
        return searchType === 'address'
          ? wasteCollectionDaysItem?.attributes?.address?.trim()
          : wasteCollectionDaysItem?.attributes?.registrationNumber?.trim()
      })
      // eslint-disable-next-line unicorn/no-array-callback-reference
      .filter(isDefined) ?? []

  return [...new Set<string>(searchData)]
}

/**
 * Parses a date string in the format "DD.MM.YYYY" and returns a Date object.
 */
const convertSlovakDateStringToDate = (date: string) => {
  const [day, month, year] = date
    .trim()
    .split('.')
    .map((datePart) => Number(datePart.trim()))

  return new Date(year, month - 1, day) // Month is 0-indexed
}

/**
 * Checks if a given date string is after the current date.
 */
const isAfterToday = (dateString: string) => {
  const dateObj = convertSlovakDateStringToDate(dateString.trim())
  const currentDate = new Date()

  return dateObj.getTime() > currentDate.getTime()
}

/**
 * Filters the date strings from pickup days object that are after today from the given pickup day entity.
 */

const temporaryGetFollowingPickupDays = (pickupDay: WasteCollectionDayEntityFragment) => {
  const pickupDates = pickupDay.attributes?.collectionDates?.trim() || ''
  // different types of delimiters are present in the data
  let pickupDatesArray = pickupDates.split(',').map((date) => date.trim())
  if (pickupDatesArray.length === 0 || pickupDatesArray.length === 1)
    pickupDatesArray = pickupDates.split(';').map((date) => date.trim())

  // eslint-disable-next-line unicorn/no-array-callback-reference
  return pickupDatesArray.filter(isAfterToday)
}

/**
 * Returns the odd and even dates for the given pickup day entity.
 */
const getOddEvenDates = (pickupDay: WasteCollectionDayEntityFragment) => {
  if (!i18n || !i18n.t) return null
  const oddWeek = pickupDay.attributes?.oddWeek
    ? `${pickupDay.attributes?.oddWeek} ${i18n?.t('pickupDays.oddWeek')}`
    : ''
  const evenWeek = pickupDay.attributes?.evenWeek
    ? `${pickupDay.attributes?.evenWeek} ${i18n?.t('pickupDays.evenWeek')}`
    : ''

  return isCurrentWeekEven()
    ? oddWeek + (oddWeek && evenWeek ? ', ' : '') + evenWeek
    : evenWeek + (oddWeek && evenWeek ? ', ' : '') + oddWeek
}

/**
 * Returns next pickup days info for the given pickup day entity.
 */
export const getNextPickupDaysInfo = (pickupDay: WasteCollectionDayEntityFragment) => {
  if (!i18n || !i18n.t) return null
  // if format of the pickup day is in odd, even weeks
  if (pickupDay.attributes?.oddWeek || pickupDay.attributes?.evenWeek) {
    return getOddEvenDates(pickupDay)
  }

  // if format of the pickup day is in following dates for a whole year
  if (pickupDay.attributes?.collectionDates) {
    const followingPickupDays = temporaryGetFollowingPickupDays(pickupDay)

    return followingPickupDays.length > 0
      ? `${pickupDay.attributes?.pickupWeekdays} ${followingPickupDays[0]}`
      : ''
  }

  return pickupDay.attributes?.pickupWeekdays ?? i18n?.t('pickupDays.noMorePickupDates')
}

/**
 * Returns string containing the dates for next pickup days from pickupDays entity
 */
export const getMoreDatesString = (pickupDay: WasteCollectionDayEntityFragment) => {
  if (!i18n || !i18n.t) return null
  // if format of the pickup day is in odd, even weeks
  if (pickupDay.attributes?.oddWeek || pickupDay.attributes?.evenWeek) {
    return getOddEvenDates(pickupDay)
  }

  // if format of the pickup day is in following dates for a whole year
  if (pickupDay.attributes?.collectionDates) {
    const followingPickupDays = temporaryGetFollowingPickupDays(pickupDay)

    if (!followingPickupDays) return i18n?.t('pickupDays.noMorePickupDates')

    return followingPickupDays.length > 1
      ? followingPickupDays.slice(1).join(', ')
      : i18n?.t('pickupDays.noMorePickupDates')
  }

  return pickupDay.attributes?.pickupWeekdays
}

export const filterAndSortPickupDaysData = (
  pickupDaysData: WasteCollectionDaysByAddressQuery | undefined,
  wasteTypesOptions: string[],
) => {
  return pickupDaysData
    ? pickupDaysData?.wasteCollectionDays?.data
        ?.filter(isDefined)
        // TODO: implement filtering in graphql rather than in the frontend
        .filter((pickupItem) =>
          pickupItem.attributes?.type
            ? wasteTypesOptions?.includes(pickupItem.attributes?.type)
            : false,
        )
        // sort by address needed for registration number search
        .sort((pickupItemA, pickupItemB) => {
          return pickupItemA.attributes?.address && pickupItemB.attributes?.address
            ? pickupItemA.attributes.address.localeCompare(pickupItemB.attributes.address, 'sk')
            : 0
        })
        // sort in order how it is set in strapi
        .sort((pickupItemA, pickupItemB) => {
          return wasteTypesOptions && pickupItemA.attributes?.type && pickupItemB.attributes?.type
            ? wasteTypesOptions.indexOf(pickupItemA.attributes.type) -
                wasteTypesOptions.indexOf(pickupItemB.attributes.type)
            : 0
        })
    : []
}

export const groupPickupDays = (
  filteredPickupDaysData: WasteCollectionDayEntityFragment[],
  searchType: SearchType,
) => {
  const groupByAttribute = searchType === 'address' ? 'registrationNumber' : 'address'

  return groupBy(
    filteredPickupDaysData,
    (pickupDay: WasteCollectionDayEntityFragment) => pickupDay.attributes?.[groupByAttribute] ?? '',
  )
}
