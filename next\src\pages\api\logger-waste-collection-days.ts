import type { NextApiRequest, NextApiResponse } from 'next'

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const { registrationNumber, wasteCollectionType } = req.body
  // eslint-disable-next-line no-console
  console.log(
    `WasteCollectionDays registrationNumber searched in ${wasteCollectionType}: ${registrationNumber}`,
  )

  res.status(200).json({ success: true })
}

export default handler
