import Pictogram from '@/src/components/lib/Pictogram/Pictogram'
import cn from '@/src/utils/cn'
import { wasteTypesMap } from '@/src/utils/wasteTypesMap'

type Props = {
  wasteType: keyof typeof wasteTypesMap
}

/**
 * Figma: TODO
 */

// more examples for waste types can be found in the WasteIcon component
const PickupDaysIcon = ({ wasteType }: Props) => {
  return wasteType in wasteTypesMap ? (
    <div
      className={cn(
        'flex size-16 flex-col items-center justify-center rounded-3xl',
        wasteTypesMap[wasteType].className,
      )}
    >
      <Pictogram
        // name={wasteTypesMap[wasteType].pictogramName}
        // TODO: when icons are fixed, use wasteTypesMap
        name="garbageBag"
        className={cn(wasteTypesMap[wasteType].pictogramClassName, 'size-12')}
      />
    </div>
  ) : null
}

export default PickupDaysIcon
