{"name": "pickup-days-import", "version": "0.0.0", "description": "This is the description of the plugin.", "strapi": {"name": "pickup-days-import", "description": "Description of pickup-days-import plugin", "kind": "plugin"}, "dependencies": {"@strapi/design-system": "^1.6.3", "@strapi/helper-plugin": "4.25.19", "@strapi/icons": "^1.6.3", "assert": "^2.1.0", "prop-types": "^15.7.2", "uuid": "^10.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@strapi/typescript-utils": "4.25.19", "@types/react": "^18.0.38", "@types/react-dom": "^18.0.28", "@types/react-router-dom": "^5.3.3", "@types/styled-components": "^5.1.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^5.3.4", "styled-components": "^5.3.6", "typescript": "5.0.4"}, "peerDependencies": {"react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0", "react-router-dom": "^5.2.0", "styled-components": "^5.2.1"}, "author": {"name": "A Strapi developer"}, "maintainers": [{"name": "A Strapi developer"}], "engines": {"node": ">=20.19.x", "npm": ">=9.0.0"}, "scripts": {"develop": "tsc -p tsconfig.server.json -w", "build": "tsc -p tsconfig.server.json"}, "license": "MIT"}