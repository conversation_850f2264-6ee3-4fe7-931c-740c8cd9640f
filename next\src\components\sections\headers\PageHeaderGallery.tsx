import { useTranslation } from 'next-i18next'
import { useCallback, useState } from 'react'
import { useOverlayTriggerState } from 'react-stately'

import SectionContainer from '@/src/components/layout/Section/SectionContainer'
import Button from '@/src/components/lib/Button/Button'
import ImagePlaceholder from '@/src/components/lib/Image/ImagePlaceholder'
import StrapiImage from '@/src/components/lib/Image/StrapiImage'
import Image<PERSON><PERSON>Box from '@/src/components/modals/gallery/ImageLightBox'
import HeaderTitleText from '@/src/components/sections/headers/HeaderTitleText'
import { GalleryHeaderSectionFragment } from '@/src/services/graphql/api'
import cn from '@/src/utils/cn'
import { isDefined } from '@/src/utils/isDefined'

type Props = {
  title: string
  perex?: string | null | undefined
  header: GalleryHeaderSectionFragment
}

/**
 * Figma: https://www.figma.com/design/2qF09hDT9QNcpdztVMNAY4/OLO-Web?node-id=1183-12889&m=dev
 */

const PageHeaderGallery = ({ title, perex, header }: Props) => {
  const { t } = useTranslation()

  const { medias } = header

  // eslint-disable-next-line unicorn/no-array-callback-reference
  const filteredImages = medias.data.filter(isDefined) ?? []
  const imageCount = filteredImages.length

  const overlayState = useOverlayTriggerState({ defaultOpen: false })
  const [initialImageIndex, setInitialImageIndex] = useState(0)

  const openAtImageIndex = useCallback(
    (index: number) => {
      setInitialImageIndex(index)
      overlayState.open()
    },
    [overlayState],
  )

  return (
    <>
      <SectionContainer background="secondary">
        <HeaderTitleText title={title} text={perex} />
        {/* Screen: desktop */}
        <div className="max-lg:hidden">
          <div className="relative lg:top-14 lg:-mt-14">
            <ul
              className={cn('grid gap-6', {
                'grid-cols-2': imageCount === 2,
                'grid-cols-3': imageCount > 2,
              })}
            >
              {filteredImages
                .map((image, index) => {
                  if (!image.attributes) return null

                  return (
                    <li
                      // eslint-disable-next-line react/no-array-index-key
                      key={index}
                      className={cn('relative overflow-hidden lg:rounded-lg', {
                        // keep the aspect-ratio of whole gallery consistent
                        'aspect-1216/440': imageCount === 1,
                        'aspect-596/440': imageCount === 2,
                        'aspect-808/440': imageCount > 2 && index === 0,
                        // first image is larger when displaying 3 images
                        'col-[1/3] row-[1/3]': imageCount > 2 && index === 0,
                        'col-[3/4] row-[1/2]': imageCount > 2 && index === 1,
                        'col-[3/4] row-[2/3]': imageCount > 2 && index === 2,
                      })}
                    >
                      <StrapiImage
                        image={image.attributes}
                        fill
                        sizes={index === 0 ? '70vw' : '30vw'}
                        className="z-1 object-cover"
                      />
                    </li>
                  )
                })
                // eslint-disable-next-line unicorn/no-array-callback-reference
                .filter(isDefined)
                .slice(0, 3)}
              <div className="absolute right-4 bottom-4 z-1">
                <Button
                  variant="category-plain"
                  className="bg-white ring-offset-content-passive-primary"
                  onPress={() => openAtImageIndex(0)}
                >
                  {t('pageHeaderGallery.buttonText')}
                </Button>
              </div>
            </ul>
          </div>
        </div>
        {/* Screen: mobile */}
        <div className="lg:hidden">
          <div className="relative -mx-4 aspect-320/174">
            {filteredImages[0].attributes ? (
              <StrapiImage
                image={filteredImages[0].attributes}
                fill
                sizes="100vw"
                className="z-1 object-cover"
              />
            ) : (
              <ImagePlaceholder />
            )}
          </div>
          <div className="absolute right-3 bottom-3 z-1">
            <Button
              variant="category-plain"
              className="bg-white"
              onPress={() => openAtImageIndex(0)}
            >
              {t('pageHeaderGallery.buttonText')}
            </Button>
          </div>
        </div>
      </SectionContainer>
      {/* This div serves as an empty space for the image to overlap correctly */}
      <div aria-hidden className="h-14 bg-background-passive-base max-lg:hidden" />
      <ImageLightBox
        onClose={() => overlayState.close()}
        isOpen={overlayState.isOpen}
        images={filteredImages}
        initialImageIndex={initialImageIndex}
        isDismissable
      />
    </>
  )
}

export default PageHeaderGallery
