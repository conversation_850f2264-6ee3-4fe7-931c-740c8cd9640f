import { useTranslation } from 'next-i18next'
import { Tab, Tab<PERSON><PERSON>, TabPanel, Tabs } from 'react-aria-components'

import WasteCollectionDaysSectionContent from '@/src/components/sections/WasteCollectionDays/WasteCollectionDaysSectionContent'
import { WasteCollectionDaysTabFragment } from '@/src/services/graphql/api'
import { isDefined } from '@/src/utils/isDefined'

type Props = {
  tabs: WasteCollectionDaysTabFragment[]
}

const WasteCollectionDaysSectionTabs = ({ tabs }: Props) => {
  const { t } = useTranslation()

  const allTypes = new Set()
  const uniqueTabs =
    tabs?.filter((tab) => {
      // filter out items that has duplicated waste type which was causing breaking the page
      if (!tab) return false

      if (allTypes.has(tab.wasteCollectionDaysType)) return false

      allTypes.add(tab.wasteCollectionDaysType)

      return true
    }) ?? []

  const filteredTabs = uniqueTabs?.filter(isDefined) ?? []

  return (
    <Tabs className="flex flex-col gap-6">
      <TabList
        aria-label={t('wasteCollectionDaysSectionTabs.aria.tabListName')}
        // "py-2 -my-2" is used to make focus ring visible
        className="negative-x-spacing -my-2 scrollbar-hide flex gap-x-4 overflow-auto overflow-y-hidden py-2"
      >
        {filteredTabs.map(({ wasteCollectionDaysType, validityLabel }) => (
          <Tab
            id={wasteCollectionDaysType}
            key={wasteCollectionDaysType}
            className="relative cursor-pointer border-b-2 border-transparent px-4 py-3 text-center whitespace-nowrap ring-offset-2 outline-hidden focus-visible:ring-3 selected:border-border-active-pressed selected:font-semibold"
          >
            {validityLabel}
          </Tab>
        ))}
      </TabList>

      {filteredTabs.map((tab) => (
        <TabPanel id={tab.wasteCollectionDaysType} key={tab.wasteCollectionDaysType}>
          <WasteCollectionDaysSectionContent
            wasteCollectionDaysType={tab.wasteCollectionDaysType}
            visibleColumns={tab.visibleColumns}
          />
        </TabPanel>
      ))}
    </Tabs>
  )
}

export default WasteCollectionDaysSectionTabs
