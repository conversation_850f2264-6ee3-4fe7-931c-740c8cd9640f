import Icon from '@/src/components/lib/Icon/Icon'
import cn from '@/src/utils/cn'

/**
 * Figma: https://www.figma.com/design/2qF09hDT9QNcpdztVMNAY4/OLO-Web?node-id=810-15763&m=dev
 */

const WasteIcon = ({
  variant,
}: {
  variant:
    | 'paper'
    | 'plastic'
    | 'glass'
    | 'mixed'
    | 'organic'
    | 'civicAmenitySite'
    | 'cookingOilsAndFats'
    | 'kitchen'
    | 'cemetery'
    | 'christmasTrees'
}) => {
  return (
    <div
      className={cn('flex h-[3rem] w-[3rem] rounded-2xl p-3 text-white', {
        'bg-background-waste-paper': variant === 'paper',
        'bg-background-waste-plastic text-content-passive-secondary': variant === 'plastic',
        'bg-background-waste-glass': variant === 'glass',
        'bg-background-waste-mixed': variant === 'mixed',
        'bg-background-waste-organic': variant === 'organic',
      })}
    >
      <Icon name="kos" />
    </div>
  )
}

export default WasteIcon
