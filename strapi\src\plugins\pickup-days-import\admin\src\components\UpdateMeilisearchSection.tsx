import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>, <PERSON>ton, Link, <PERSON>, <PERSON>ack, Typography } from '@strapi/design-system'
import axiosInstance from '../utils/axiosInstance'

const updateMeilisearchUrls = {
  'pickup-days': '/pickup-days/update-meilisearch-pickup-days',
}

const headerTexts = {
  'pickup-days': 'Aktualizácia manuálne vykonaných zmien',
}

const links = {
  'pickup-days': () =>
    `/content-manager/collectionType/api::pickup-day.pickup-day`,
}

type UpdateMeilisearchSectionProps = {
  type: 'pickup-days'
}

const UpdateMeilisearchSection = ({ type }: UpdateMeilisearchSectionProps) => {
  // TODO: this is not currently used
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState<any>(null)
  const [error, setError] = useState<any>(null)

  const handleSubmit = () => {
    setLoading(true)
    setSuccess(null)
    setError(null)

    axiosInstance
      .post(updateMeilisearchUrls[type])
      .then((response) => {
        setSuccess(response)
      })
      .catch((error) => {
        setError(error)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  return (
    <Box
      background="neutral0"
      hasRadius
      shadow="filterShadow"
      paddingTop={6}
      paddingBottom={6}
      paddingLeft={7}
      paddingRight={7}
    >
      <Stack spacing={4}>
        <Typography variant="delta" as="h2">
          {headerTexts[type]}
        </Typography>
        {loading && <Loader />}
        {success && (
          <Alert
            title="Aktualizácia zmien úspešná"
            action={success.data?.importId && <Link to={links[type]()}>Zobraziť Odvozové dni</Link>}
            variant="success"
            onClose={() => setSuccess(null)}
          >
            {success.data.message}
          </Alert>
        )}
        {error && (
          <Alert
            title="Aktualizácia zmien neúspešná"
            variant="danger"
            onClose={() => setError(null)}
          >
            {error?.response?.data?.message ?? error.toString()}
          </Alert>
        )}
        <div>
          <Button onClick={handleSubmit} loading={loading}>
            Aktualizovať odvozové dni
          </Button>
        </div>
      </Stack>
    </Box>
  )
}

export default UpdateMeilisearchSection
