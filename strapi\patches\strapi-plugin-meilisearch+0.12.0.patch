diff --git a/node_modules/strapi-plugin-meilisearch/server/services/lifecycle/lifecycle.js b/node_modules/strapi-plugin-meilisearch/server/services/lifecycle/lifecycle.js
index 7d0b6ac..01dd065 100644
--- a/node_modules/strapi-plugin-meilisearch/server/services/lifecycle/lifecycle.js
+++ b/node_modules/strapi-plugin-meilisearch/server/services/lifecycle/lifecycle.js
@@ -12,6 +12,12 @@ module.exports = ({ strapi }) => {
      * @returns {Promise<object>}
      */
     async subscribeContentType({ contentType }) {
+      // Each time waste collection day or pickup day is imported it causes to refresh the meilisearch which is
+      // extremely slow.
+      if ((contentType === 'api::waste-collection-day.waste-collection-day') || (contentType === 'api::pickup-day.pickup-day')) {
+        return Promise.resolve({})
+      }
+
       const contentTypeUid = contentTypeService.getContentTypeUid({
         contentType: contentType,
       })
