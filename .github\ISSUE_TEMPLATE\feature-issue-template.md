---
name: Feature issue template
about: Describe this issue template's purpose here.
title: ''
labels: ''
assignees: ''

---

### Context

Provide background information about the task, including why it matters and its impact.

### Description

Detail the specific feature, functionality, or change being proposed.

### Acceptance criteria

Define the specific conditions or requirements that must be met for the work to be considered complete.

### Preconditions and Dependencies

List any conditions that must be satisfied before work can begin or be completed.

### Possible Implementation

Outline potential approaches to solving the problem.

### Documentation

Include relevant resources, such as a Figma design link, technical documents, or reference materials needed to support
the task.
